## 动态迁移蓝图 (V1.4)

**最后更新时间:** 2025-07-16T21:18:47+08:00

### 1. 文件迁移映射表

根据对 `model.py` 的分析，我们补充所有依赖的骨干网络文件，并更新模型工厂的状态。

| 源文件 (LORE-TSR) | 目标文件 (train-anything) | 迁移策略 (说明) | 状态 |
| :--- | :--- | :--- | :--- |
| **入口与配置** | | | |
| `src/main.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | **重构**: 以 `cycle-centernet-ms` 为模板，适配 `accelerate` 训练循环。 | `未开始` |
| `src/lib/opts.py` | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | **重构**: 转换为 `OmegaConf` 的 YAML 格式。 | `未开始` |
| **数据集** | | | |
| `src/lib/datasets/dataset_factory.py` | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | **重构**: 创建新的 `lore_tsr_dataset.py`。 | `未开始` |
| `src/lib/datasets/table_dataset/table.py` | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | **重构**: 核心逻辑将被整合进 `lore_tsr_dataset.py`。 | `未开始` |
| **训练器/检测器** | | | |
| `src/lib/detectors/base_detector.py` | (由框架取代) | **废弃**: 由 `training_loop` 取代。 | `未开始` |
| `src/lib/detectors/ctdet.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | **重构**: 核心 `run_epoch` 逻辑将被迁移。 | `未开始` |
| **核心模型与算法** | | | |
| `src/lib/models/model.py` | `networks/lore_tsr/model_factory.py` | **复制与适配**: `create_model` 逻辑。 | `进行中` |
| `src/lib/models/networks/pose_dla_dcn.py` | `networks/lore_tsr/pose_dla_dcn.py` | **复制**: DLA骨干网络。 | `已完成` |
| `src/lib/models/networks/fpn_resnet.py` | `networks/lore_tsr/fpn_resnet.py` | **复制**: FPN-ResNet骨干网络。 | `已完成` |
| `src/lib/models/networks/fpn_resnet_half.py`| `networks/lore_tsr/fpn_resnet_half.py` | **复制**: FPN-ResNet (Half) 骨干网络。 | `已完成` |
| `src/lib/models/networks/fpn_mask_resnet.py`| `networks/lore_tsr/fpn_mask_resnet.py` | **复制**: FPN-Mask-ResNet 骨干网络。 | `已完成` |
| `src/lib/models/networks/fpn_mask_resnet_half.py`| `networks/lore_tsr/fpn_mask_resnet_half.py` | **复制**: FPN-Mask-ResNet (Half) 骨干网络。 | `已完成` |
| `src/lib/models/losses.py` | `networks/lore_tsr/losses.py` | **复制**: 核心损失函数。 | `已完成` |
| `src/lib/utils/post_process.py` | `networks/lore_tsr/post_process.py` | **复制**: 核心后处理逻辑。 | `已完成` |
| **编译依赖** | | | |
| `src/lib/external/DCNv2/` | `external/lore_tsr/DCNv2/` | **复制与隔离**: 复制源代码。 | `已完成` |
| `src/lib/external/nms.pyx` | `external/lore_tsr/nms.pyx` | **复制与隔离**: 复制源代码。 | `已完成` |

### 2. 逻辑依赖与迁移路径图 (Mermaid)

```mermaid
graph TD
    subgraph "Source: LORE-TSR"
        direction LR
        S_model("src/lib/models/model.py")
        S_dla("networks/pose_dla_dcn.py")
        S_fpn("networks/fpn_resnet.py")
        S_fpn_half("networks/fpn_resnet_half.py")
        S_fpn_mask("networks/fpn_mask_resnet.py")
        S_fpn_mask_half("networks/fpn_mask_resnet_half.py")
        S_losses("src/lib/models/losses.py")
        S_post_process("src/lib/utils/post_process.py")
        S_dcn("src/lib/external/DCNv2/")
        S_nms("src/lib/external/nms.pyx")
    end

    subgraph "Target: train-anything"
        direction LR
        T_model_factory("networks/lore_tsr/model_factory.py")
        T_dla("networks/lore_tsr/pose_dla_dcn.py")
        T_fpn("networks/lore_tsr/fpn_resnet.py")
        T_fpn_half("networks/lore_tsr/fpn_resnet_half.py")
        T_fpn_mask("networks/lore_tsr/fpn_mask_resnet.py")
        T_fpn_mask_half("networks/lore_tsr/fpn_mask_resnet_half.py")
        T_losses("networks/lore_tsr/losses.py")
        T_post_process("networks/lore_tsr/post_process.py")
        T_dcn("external/lore_tsr/DCNv2/")
        T_nms("external/lore_tsr/nms.pyx")
    end

    %% 源项目内部依赖关系
    S_model -.-> S_dla
    S_model -.-> S_fpn
    S_model -.-> S_fpn_half
    S_model -.-> S_fpn_mask
    S_model -.-> S_fpn_mask_half
    S_model -.-> S_losses

    %% 迁移路径
    S_model -- "Copy & Adapt" --> T_model_factory
    S_dla -- Copy --> T_dla
    S_fpn -- Copy --> T_fpn
    S_fpn_half -- Copy --> T_fpn_half
    S_fpn_mask -- Copy --> T_fpn_mask
    S_fpn_mask_half -- Copy --> T_fpn_mask_half
    S_losses -- Copy --> T_losses

    %% 目标项目内部依赖关系
    T_model_factory -.-> T_dla
    T_model_factory -.-> T_fpn
    T_model_factory -.-> T_fpn_half
    T_model_factory -.-> T_fpn_mask
    T_model_factory -.-> T_fpn_mask_half
    T_model_factory -.-> T_losses
```

### 3. 目标目录结构树

```
train-anything/
├── configs/
│   └── table_structure_recognition/
│       └── lore_tsr/
│           └── lore_tsr_config.yaml  [placeholder]
├── modules/
│   └── utils/
│       └── lore_tsr/                   [新增]
│           ├── __init__.py             [新增]
│           ├── utils.py                [新增]
│           └── image.py                [新增]
├── external/
│   └── lore_tsr/
│       ├── DCNv2/                    [已复制]
│       └── nms.pyx                   [已复制]
├── my_datasets/
│   └── table_structure_recognition/
│       └── lore_tsr_dataset.py     [placeholder]
├── networks/
│   └── lore_tsr/
│       ├── pose_dla_dcn.py               [已复制]
│       ├── fpn_resnet.py                 [已复制]
│       ├── fpn_resnet_half.py            [已复制]
│       ├── fpn_mask_resnet.py            [已复制]
│       ├── fpn_mask_resnet_half.py       [已复制]
│       ├── losses.py                     [已复制]
│       ├── model_factory.py              [进行中]
│       └── post_process.py               [已复制]
└── training_loops/
|    └── table_structure_recognition/
|        └── train_lore_tsr.py       [placeholder]
|
...

```

---

## 编码计划 (Step 4)

### 步骤 4.1: 迁移并适配模型工厂 (`model_factory.py`)

**目标:** 迁移 `LORE-TSR` 中负责创建和加载模型的工厂函数 `create_model`。这是连接配置文件和模型实体的关键“胶水代码”。我们将遵循“复制与适配”策略，并确保所有依赖的骨干网络导入路径正确无误,为后续与 `train-anything` 框架的深度集成做准备。

**影响文件:**
*   `train-anything/networks/lore_tsr/model_factory.py` (创建并填充)

**编码指令:**

1.  **创建并复制核心逻辑:**
    *   **源文件:** `LORE-TSR/src/lib/models/model.py`
    *   **目标文件:** `train-anything/networks/lore_tsr/model_factory.py`
    *   **操作:** 创建目标文件，并将源文件中的 `create_model` 函数、`_model_factory` 字典以及所有相关的 `import` 语句完整复制到目标文件中。暂时忽略 `load_model` 和 `save_model`， 我们将在后续步骤中处理检查点逻辑。

2.  **全面适配导入路径:**
    *   **上下文:** 所有从 `models.networks` 的导入都需要更新为新目录结构下的相对导入。
    *   **操作:** 在 `model_factory.py` 文件顶部，将所有导入语句修改为相对路径，如下所示：
        *   `from .networks.fpn_resnet import get_pose_net_fpn` -> `from .fpn_resnet import get_pose_net_fpn`
        *   `from .networks.fpn_resnet_half import get_pose_net_fpn_half` -> `from .fpn_resnet_half import get_pose_net_fpn_half`
        *   `from .networks.fpn_mask_resnet import get_pose_net_fpn_mask` -> `from .fpn_mask_resnet import get_pose_net_fpn_mask`
        *   `from .networks.fpn_mask_resnet_half import get_pose_net_fpn_mask_half` -> `from .fpn_mask_resnet_half import get_pose_net_fpn_mask_half`
        *   `from .networks.pose_dla_dcn import get_pose_net as get_dla_dcn` -> `from .pose_dla_dcn import get_pose_net as get_dla_dcn`

**验收标准:**

1.  **文件存在性:**
    *   验证路径 `train-anything/networks/lore_tsr/model_factory.py` 下的文件已成功创建。
2.  **内容校验:**
    *   打开该文件，确认其中包含 `_model_factory` 字典和 `create_model` 函数。
    *   逐一确认所有 `import` 语句都已按要求修改为正确的相对路径格式 (例如 `from .fpn_resnet...`)。

---
