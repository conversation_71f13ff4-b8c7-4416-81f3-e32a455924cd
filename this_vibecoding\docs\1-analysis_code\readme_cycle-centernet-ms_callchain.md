# Cycle-CenterNet-MS（ModelScope） 调用链分析

## 调用链（Call Chain）

### 节点：脚本全局作用域 (`train_cycle_centernet_ms.py`)
- **文件路径**：`train-anything/training_loops/table_structure_recognition/train_cycle_centernet_ms.py`
- **功能说明**：作为训练流程的入口，负责初始化环境、解析配置、设置日志，并启动整个训练过程。
- **输入参数**：通过命令行传入的参数，由 `parse_args` 函数解析。
- **输出说明**：无直接返回值，其执行会产生训练好的模型文件、日志文件和验证结果。
- **节点流程可视化**:

```mermaid
flowchart TD
    A[开始] --> B(导入模块);
    B --> C["解析命令行参数 config = parse_args()"];
    C --> D(初始化日志记录器);
    D --> E(设置 PyTorch 环境);
    E --> F(调用 main 函数或执行主训练逻辑);
    F --> G[结束];
```

#### 节点：`parse_args`
- **文件路径**：`train-anything/modules/proj_cmd_args/cycle_centernet/args.py`
- **功能说明**：解析命令行参数，加载 YAML 配置文件，并允许通过命令行覆盖配置项。这是整个训练脚本获取配置的入口。
- **输入参数**：无（内部读取 `sys.argv`）。
- **输出说明**：返回一个 `OmegaConf.DictConfig` 对象，该对象以层级结构存储了所有配置信息。
- **节点流程可视化**:

```mermaid
flowchart TD
    A[开始] --> B(调用 preprocess_args 预处理命令行参数);
    B --> C["使用 argparse 解析处理后的参数"];
    C --> D["获取配置文件路径和覆盖参数 args.config, args.override"];
    D --> E["调用 load_config(args.config, args.override)"];
    E --> F[返回 config 对象];
    F --> G[结束];
```

#### 节点：`load_config`
- **文件路径**：`train-anything/modules/proj_cmd_args/cycle_centernet/config_parser.py`
- **功能说明**：负责从指定的 YAML 文件加载基础配置，并将其与命令行提供的覆盖参数进行合并，生成最终的配置对象。
- **输入参数**：
    - `config_path` (str): 主配置文件的路径。
    - `overrides` (Optional[List[str]]): 从命令行传入的需要覆盖的参数列表，例如 `['training.epochs=100', 'basic.debug=true']`。
- **输出说明**：返回一个 `OmegaConf.DictConfig` 对象，代表了合并后的最终配置。
- **节点流程可视化**:

```mermaid
flowchart TD
    A[开始] --> B{检查配置文件是否存在};
    B --> C(使用 `OmegaConf.load` 加载基础配置);
    C --> D{判断 `overrides` 是否存在};
    D -- 是 --> E(调用 `parse_override_args` 解析覆盖参数);
    E --> F(使用 `OmegaConf.merge` 合并基础配置和覆盖配置);
    F --> G[返回合并后的 `config` 对象];
    D -- 否 --> G;
    G --> H[结束];
```

#### 节点：`create_file_logger`
- **文件路径**：`train-anything/modules/utils/log.py`
- **功能说明**：使用 `logzero` 库创建一个配置好的文件日志记录器，用于将日志信息写入指定文件，并支持日志轮转。
- **输入参数**：
    - `log_path` (str): 日志文件的保存路径。
    - `level` (str): 日志级别，默认为 'INFO'。
- **输出说明**：返回一个 `logzero` 的 `Logger` 对象，该对象可用于记录日志。
- **节点流程可视化**:

```mermaid
flowchart TD
    A[开始] --> B(调用 `logzero.setup_logger`);
    B --> C{配置日志参数：路径、级别、格式等};
    C --> D[返回 `file_logger` 对象];
    D --> E[结束];
```

### 节点：`main`
- **文件路径**：`train-anything/training_loops/table_structure_recognition/train_cycle_centernet_ms.py`
- **功能说明**：作为训练脚本的主函数，负责协调整个训练生命周期，包括环境准备、模型创建、数据加载、训练循环、验证和模型保存。
- **输入参数**：无。
- **输出说明**：无返回值。
- **核心逻辑**:
    1. **迭代**: 通过 `for epoch in range(...)` 和 `for step, batch in enumerate(train_dataloader)` 嵌套循环来遍历训练数据。
    2. **前向传播**: 将批次数据 `batch['images']` 送入模型，得到预测结果 `predictions`。
    3. **目标准备**: 调用 `prepare_targets` 函数，根据模型输出的特征图尺寸，生成用于计算损失的真实标签 `targets`。
    4. **损失计算**: 将 `predictions` 和 `targets` 送入损失函数 `criterion`，计算出总损失和各个分项损失。
    5. **反向传播**: 调用 `accelerator.backward(loss)` 计算梯度。
    6. **权重更新**: 执行 `optimizer.step()` 和 `lr_scheduler.step()` 更新模型参数和学习率。
    7. **日志记录**: 定期使用 `accelerator.log` 记录训练损失、学习率等指标。
    8. **验证与保存**: 根据 `config.checkpoint.validation.steps` 的设置，定期调用 [log_validation](cci:1://file:///d:/workspace/projects/TSRTransplantation/train-anything/training_loops/table_structure_recognition/train_cycle_centernet_ms.py:197:0-350:19) 函数在验证集上评估模型，并调用 [save_best_checkpoints](cci:1://file:///d:/workspace/projects/TSRTransplantation/train-anything/training_loops/table_structure_recognition/train_cycle_centernet_ms.py:135:0-194:9) 保存性能最佳的模型。
- **节点流程可视化**:

```mermaid
flowchart TD
    A[开始] --> B(调用 `prepare_training_enviornment_v2` 准备 Accelerate 环境);
    B --> C(调用 `load_checkpoint_state` 加载检查点);
    C --> D(调用 `create_model_and_ema` 创建模型和 EMA 处理器);
    D --> E(调用 `setup_training_components` 设置优化器、损失函数、调度器);
    E --> F(调用 `prepare_dataloaders` 准备训练和验证数据加载器);
    F --> G{检查并处理特殊模式（干运行/仅可视化）};
    G --> H(调用 `prepare_accelerator_components` 准备分布式训练组件);
    H --> I(调用 `calculate_final_training_steps` 计算总训练步数);
    I --> J(调用 `run_training_loop` 执行主训练循环);
    J --> K(调用 `save_final_model` 保存最终模型);
    K --> L[结束];
```

### 节点：`prepare_training_enviornment_v2`
- **文件路径**：`train-anything/modules/utils/train_utils.py`
- **功能说明**：该函数负责准备分布式训练环境。它基于配置文件初始化 `accelerate.Accelerator`，设置混合精度、日志记录器、项目配置，并根据主进程设置不同的日志级别。此外，它还设置全局随机种子以确保实验的可复现性，并创建输出目录。
- **输入参数**：
    - `config` (OmegaConf.DictConfig): 包含所有训练配置的 OmegaConf 对象。
    - `logger`: 日志记录器实例。
- **输出说明**：
    - `accelerator` (accelerate.Accelerator): 初始化后的 Accelerator 实例。
    - `weight_dtype` (torch.dtype): 根据混合精度配置（fp16, bf16, or float32）确定的权重数据类型。
- **节点流程可视化**:

```mermaid
flowchart TD
    A[开始] --> B{读取分布式配置};
    B --> C(初始化 `accelerate.Accelerator`);
    C --> D(配置 `logging` 和 `transformers` 日志);
    D --> E{根据混合精度设置 `weight_dtype`};
    E --> F(使用 `set_seed` 设置随机种子);
    F --> G(创建输出目录);
    G --> H[返回 `accelerator` 和 `weight_dtype`];
    H --> I[结束];
```

### 节点：`load_checkpoint_state`
- **文件路径**：`train-anything/training_loops/table_structure_recognition/train_cycle_centernet_ms.py`
- **功能说明**：该函数负责从指定的检查点恢复训练状态。它会检查配置中的 `resume_from_checkpoint` 路径，如果路径存在，则加载模型权重、EMA 状态、优化器状态、学习率调度器状态以及训练进度（如 `global_step` 和 `epoch`），从而实现断点续训。
- **输入参数**：
    - `config` (OmegaConf.DictConfig): 包含所有训练配置的 OmegaConf 对象。
    - `accelerator` (accelerate.Accelerator): Accelerator 实例。
    - `logger`: 日志记录器实例。
    - `file_logger`: 文件日志记录器实例。
- **输出说明**：一个元组，包含：
    - `start_steps` (int): 恢复的全局步数。
    - `start_epoch` (int): 恢复的起始轮次。
    - `ema_path` (str or None): EMA 模型的路径。
    - `model_state_dict` (dict or None): 模型的状态字典。
    - `optimizer_ckpt` (dict or None): 优化器的状态字典。
    - `lr_scheduler_ckpt` (dict or None): 学习率调度器的状态字典。
    - `load_state_dict_msg` (str or None): 加载状态字典时的消息。
- **节点流程可视化**:

```mermaid
flowchart TD
    A[开始] --> B{检查 `config.training.resume_from_checkpoint` 是否设置};
    B -- Y --> C{确定检查点类型（latest, best_loss_model, 或具体路径）};
    C --> D(加载模型、优化器、调度器等状态文件);
    D --> E(更新 `start_steps` 和 `start_epoch`);
    E --> F[返回所有加载的状态];
    B -- N --> G[返回初始默认值];
    F --> H[结束];
    G --> H;
```

### 节点：`create_model_and_ema_handler`
- **文件路径**：`train-anything/training_loops/table_structure_recognition/train_cycle_centernet_ms.py`
- **功能说明**：此函数负责创建模型实例和指数移动平均（EMA）处理器。它首先调用 `create_cycle_centernet_ms_model` 来构建 Cycle-CenterNet 模型，然后根据配置决定是否启用 EMA。如果启用，它会初始化一个 `EMAHandler` 并加载其状态（如果从检查点恢复）。
- **输入参数**：
    - `config` (OmegaConf.DictConfig): 包含所有训练配置的 OmegaConf 对象。
    - `accelerator` (accelerate.Accelerator): Accelerator 实例。
    - `model_state_dict` (dict or None): 从检查点加载的模型状态字典。
    - `ema_path` (str or None): EMA 模型的路径。
    - `weight_dtype` (torch.dtype): 权重的数据类型。
    - `load_state_dict_msg` (str or None): 加载状态字典时的消息。
- **输出说明**：一个元组，包含：
    - `model` (torch.nn.Module): 创建的 Cycle-CenterNet 模型。
    - `ema_handler` (EMAHandler or None): 创建的 EMA 处理器或 None。
- **节点流程可视化**:

```mermaid
flowchart TD
    A[开始] --> B(调用 `create_cycle_centernet_ms_model` 创建模型);
    B --> C{检查 `model_state_dict` 是否存在};
    C -- Y --> D(加载模型权重);
    C -- N --> B;
    D --> E{检查 `config.ema.enabled` 是否为 True};
    E -- Y --> F(创建 `EMAHandler`);
    F --> G{检查 `ema_path` 是否存在};
    G -- Y --> H(加载 EMA 状态);
    H --> I[返回 `model` 和 `ema_handler`];
    G -- N --> I;
    E -- N --> J[返回 `model` 和 `None`];
    I --> K[结束];
    J --> K;
```

### 节点：`setup_training_components`
- **文件路径**：`train-anything/training_loops/table_structure_recognition/train_cycle_centernet_ms.py`
- **功能说明**：此函数负责设置训练过程中的核心组件。它首先调用 `create_cycle_centernet_ms_loss` 创建损失函数，然后使用 `get_optimizer` 初始化优化器，并调用 `prepare_dataloaders` 准备数据集。最后，它根据总训练步数，通过 `get_scheduler` 创建学习率调度器。
- **输入参数**：
    - `config` (OmegaConf.DictConfig): 包含所有训练配置的 OmegaConf 对象。
    - `model` (torch.nn.Module): 训练模型。
    - `optimizer_ckpt` (dict or None): 优化器的状态字典。
    - `lr_scheduler_ckpt` (dict or None): 学习率调度器的状态字典。
    - `accelerator` (accelerate.Accelerator): Accelerator 实例。
- **输出说明**：一个元组，包含：
    - `loss_criterion` (torch.nn.Module): 损失函数。
    - `optimizer` (torch.optim.Optimizer): 优化器。
    - `lr_scheduler`: 学习率调度器。
    - `max_train_steps` (int): 最大训练步数。
    - `train_datasets` (list): 训练数据集列表。
    - `train_loaders` (list): 训练数据加载器列表。
    - `seed` (int): 随机种子。
- **节点流程可视化**:

```mermaid
flowchart TD
    A[开始] --> B(调用 `create_cycle_centernet_ms_loss` 创建损失函数);
    B --> C(调用 `get_optimizer` 创建优化器);
    C --> D(调用 `prepare_dataloaders` 准备数据集);
    D --> E(计算 `max_train_steps`);
    E --> F(调用 `get_scheduler` 创建学习率调度器);
    F --> G[返回所有组件];
    G --> H[结束];
```

### 节点：`prepare_dataloaders`
- **文件路径**：`train-anything/training_loops/table_structure_recognition/train_cycle_centernet_ms.py`
- **功能说明**：该函数根据指定的模式（'train' 或 'val'）创建并返回相应的数据集和数据加载器。它会读取配置文件中指定的数据路径，初始化 `TableDataset`，并最终封装成 `torch.utils.data.DataLoader`，以便在训练和验证循环中使用。它还支持调试模式，在该模式下只加载少量数据。
- **输入参数**：
    - `config` (OmegaConf.DictConfig): 包含所有训练配置的 OmegaConf 对象。
    - `mode` (str): 数据加载模式，'train' 或 'val'。
    - `train_batch_size_per_device` (int): 每个设备上的批处理大小。
    - `seed` (int): 用于数据加载器工作进程的随机种子。
- **输出说明**：一个元组，包含：
    - `datasets` (list): 创建的数据集对象列表。
    - `loaders` (list): 创建的数据加载器列表，每个元素是一个 `(name, DataLoader)` 元组。
- **节点流程可视化**:

```mermaid
flowchart TD
    A[开始] --> B{根据 `mode` 选择数据目录};
    B -- train --> C[获取训练数据路径];
    B -- val --> D[获取验证数据路径];
    C --> E(初始化 `TableDataset`);
    D --> E;
    E --> F(创建 `torch.utils.data.DataLoader`);
    F --> G[返回 `datasets` 和 `loaders`];
    G --> H[结束];
```

### 节点：`handle_dry_run_mode`
- **文件路径**：`train-anything/training_loops/table_structure_recognition/train_cycle_centernet_ms.py`
- **功能说明**：处理“干运行”模式。如果配置中启用了此模式，该函数将不会执行训练，而是加载少量数据批次，并使用 `DryRunVisualizer` 对其进行可视化，以帮助调试数据预处理和加载流程。执行完毕后，程序将退出。
- **输入参数**：
    - `config` (OmegaConf.DictConfig): 包含所有训练配置的 OmegaConf 对象。
    - `model` (torch.nn.Module): 训练模型。
    - `accelerator` (accelerate.Accelerator): Accelerator 实例。
    - `weight_dtype` (torch.dtype): 权重的数据类型。
    - `train_loaders` (list): 训练数据加载器列表。
- **输出说明**：
    - `bool`: 如果执行了干运行模式，则返回 `True`，否则返回 `False`。
- **节点流程可视化**:

```mermaid
flowchart TD
    A[开始] --> B{检查 `config.debug_visualization.dry_run` 是否启用};
    B -- Y --> C(导入并创建 `DryRunVisualizer`);
    C --> D(遍历少量数据批次);
    D --> E(调用 `prepare_targets` 生成目标);
    E --> F(调用 `visualize_batch` 进行可视化);
    F --> G[返回 `True` 并准备退出];
    B -- N --> H[返回 `False`];
    G --> I[结束];
    H --> I;
```

### 节点：`initialize_best_model_record`
- **文件路径**：`train-anything/training_loops/table_structure_recognition/train_cycle_centernet_ms.py`
- **功能说明**：初始化或加载用于跟踪最佳模型的记录。如果记录文件（`record.json`）存在，则从中加载历史最佳损失值；否则，初始化一个包含无限大损失值的记录字典，用于首次比较。
- **输入参数**：
    - `best_loss_model_record` (str): 存储最佳模型记录的 JSON 文件路径。
- **输出说明**：
    - `dict`: 包含历史最佳损失值的字典，例如 `{'avg_loss': 0.123}`。
- **节点流程可视化**:

```mermaid
flowchart TD
    A[开始] --> B{检查 `best_loss_model_record` 文件是否存在};
    B -- Y --> C(读取 JSON 文件并加载 `avg_loss`);
    B -- N --> D(初始化 `avg_loss` 为无穷大);
    C --> E[返回记录字典];
    D --> E;
    E --> F[结束];
```

### 节点：`prepare_accelerator_components`
- **文件路径**：`train-anything/training_loops/table_structure_recognition/train_cycle_centernet_ms.py`
- **功能说明**：该函数使用 `accelerate.Accelerator` 来准备训练所需的各个组件，包括模型、优化器、学习率调度器和数据加载器，使其适应分布式训练环境。一个关键特性是，在分布式训练模式下，它通过猴子补丁（monkey-patching）的方式修改 `torch.nn.parallel.DistributedDataParallel`，强制设置 `find_unused_parameters=True`，以解决模型中存在未参与损失计算的参数时导致的 DDP 错误。
- **输入参数**：
    - `accelerator` (accelerate.Accelerator): Accelerator 实例。
    - `model` (torch.nn.Module): 待准备的模型。
    - `optimizer` (torch.optim.Optimizer): 待准备的优化器。
    - `lr_scheduler`: 待准备的学习率调度器。
    - `train_loaders` (list): 待准备的训练数据加载器列表。
- **输出说明**：一个元组，包含准备好的组件：
    - `model` (torch.nn.Module): 准备好的模型。
    - `optimizer` (torch.optim.Optimizer): 准备好的优化器。
    - `lr_scheduler`: 准备好的学习率调度器。
    - `train_loaders` (list): 准备好的数据加载器列表。
- **节点流程可视化**:

```mermaid
flowchart TD
    A[开始] --> B{检查是否为分布式训练};
    B -- Y --> C(猴子补丁 `DDP`，设置 `find_unused_parameters=True`);
    C --> D(调用 `accelerator.prepare` 准备 `model`, `optimizer`, `lr_scheduler`);
    D --> E(恢复原始 `DDP` 类);
    B -- N --> F(直接调用 `accelerator.prepare` 准备 `model`, `optimizer`, `lr_scheduler`);
    E --> G(遍历并准备所有 `train_loaders`);
    F --> G;
    G --> H[返回所有准备好的组件];
    H --> I[结束];
```

### 节点：`calculate_final_training_steps`
- **文件路径**：`train-anything/training_loops/table_structure_recognition/train_cycle_centernet_ms.py`
- **功能说明**：在 `accelerator` 处理完数据加载器后，重新计算总训练步数。由于 `accelerator.prepare` 可能会改变数据加载器的长度（尤其是在分布式训练中），此函数确保训练步数计算的准确性。它会取 `prepare` 前后计算出的总步数的最大值，并记录详细的训练配置信息。
- **输入参数**：
    - `config` (OmegaConf.DictConfig): 包含所有训练配置的 OmegaConf 对象。
    - `accelerator` (accelerate.Accelerator): Accelerator 实例。
    - `train_loaders` (list): 准备好的训练数据加载器列表。
    - `max_train_steps` (int): `prepare` 前计算出的最大训练步数。
    - `train_datasets` (list): 训练数据集列表。
    - `seed` (int): 训练使用的随机种子。
- **输出说明**：
    - `int`: 最终确定的最大训练步数。
- **节点流程可视化**:

```mermaid
flowchart TD
    A[开始] --> B(计算 `prepare` 后的 `train_loaders` 总长度);
    B --> C(根据新长度和总 epoch 数计算新的 `max_train_steps`);
    C --> D{比较新旧 `max_train_steps`};
    D --> E[取两者中的最大值作为最终训练步数];
    E --> F{是否为主进程};
    F -- Y --> G(记录详细的训练信息，如数据集大小、批处理大小、总步数等);
    G --> H[返回最终训练步数];
    F -- N --> H;
    H --> I[结束];
```

### 节点：`run_training_loop`
- **文件路径**：`train-anything/training_loops/table_structure_recognition/train_cycle_centernet_ms.py`
- **功能说明**：这是核心的训练循环函数。它遍历指定的 epoch 和数据加载器，执行完整的训练步骤，包括：前向传播、目标生成、损失计算、反向传播、优化器更新和学习率调度。该函数特别处理了 DDP 模式下的“未使用参数”问题，并集成了 EMA 模型更新、进度条日志、周期性验证和检查点保存功能。
- **输入参数**：
    - `config` (OmegaConf.DictConfig): 训练配置。
    - `model` (torch.nn.Module): 训练模型。
    - `ema_handler`: EMA 模型处理器。
    - `loss_criterion`: 损失函数。
    - `optimizer`: 优化器。
    - `lr_scheduler`: 学习率调度器。
    - `accelerator` (accelerate.Accelerator): Accelerator 实例。
    - `weight_dtype` (torch.dtype): 权重数据类型。
    - `train_loaders` (list): 训练数据加载器。
    - `val_loaders` (list): 验证数据加载器。
    - `global_step` (int): 当前的全局步数。
    - `first_epoch` (int): 开始的 epoch 编号。
    - `max_train_steps` (int): 最大训练步数。
    - `progress_bar`: tqdm 进度条实例。
    - `best_loss_model_record` (str): 最佳模型记录文件路径。
    - `best_loss_record_data` (dict): 最佳损失记录数据。
- **输出说明**：
    - `int`: 训练结束时的全局步数。
- **节点流程可视化**:

```mermaid
flowchart TD
    A[开始] --> B(进入 Epoch 循环);
    B --> C(进入 Step 循环，遍历 train_loaders);
    C --> D(获取数据批次并进行前向传播);
    D --> E(生成 targets);
    E --> F(计算损失，修复 avg_factor);
    F --> G(聚合所有分支损失以兼容 DDP);
    G --> H("反向传播 accelerator.backward(loss)");
    H --> I(梯度裁剪);
    I --> J(更新优化器和学习率);
    J --> K{是否启用 EMA?};
    K -- Y --> L(更新 EMA 模型);
    L --> M(更新进度条和日志);
    K -- N --> M;
    M --> N{是否达到保存步数?};
    N -- Y --> O(保存检查点);
    O --> P(执行验证并保存最佳模型);
    P --> Q{是否达到最大训练步数?};
    N -- N --> Q;
    Q -- Y --> S[结束训练];
    Q -- N --> C;
    B -- 训练结束 --> R(返回最终 global_step);
    S --> R;
    R --> T[结束];
```

### 节点：`save_final_model`
- **文件路径**：`train-anything/training_loops/table_structure_recognition/train_cycle_centernet_ms.py`
- **功能说明**：在训练结束后，保存最终的模型状态。该函数会等待所有分布式进程完成同步，然后由主进程调用 `save_state` 函数，将模型、优化器、学习率调度器、EMA 模型（如果启用）以及训练状态（如 `global_step`）保存到输出目录下的 `model_final` 文件夹中。
- **输入参数**：
    - `config` (OmegaConf.DictConfig): 训练配置。
    - `model` (torch.nn.Module): 训练模型。
    - `optimizer`: 优化器。
    - `lr_scheduler`: 学习率调度器。
    - `accelerator` (accelerate.Accelerator): Accelerator 实例。
    - `ema_handler`: EMA 模型处理器。
    - `global_step` (int): 最终的全局步数。
- **输出说明**：无。该函数执行文件写入操作。
- **节点流程可视化**:

```mermaid
flowchart TD
    A[开始] --> B("调用 accelerator.wait_for_everyone() 进行同步");
    B --> C{检查是否为主进程};
    C -- Y --> D(构建最终模型保存路径);
    D --> E(调用 save_state 保存完整训练状态);
    E --> F[结束];
    C -- N --> F;
```

---

## `run_training_loop` 内部调用链

### 子节点：`prepare_targets`
- **文件路径**：`train-anything/my_datasets/table_structure_recognition/target_preparation.py`
- **功能说明**：该函数是目标生成的核心。它接收一批次的数据，并根据模型输出的特征图尺寸，将其转换为训练所需的所有目标张量。它会遍历批次中的每个样本，提取标注信息（中心点、顶点等），然后调用其他辅助函数（如 `create_gaussian_heatmap_target`, `create_offset_target`）来生成热力图、偏移量、中心-顶点向量等目标，并将它们打包成一个字典返回。
- **输入参数**：
    - `batch_data` (dict): 从数据加载器传来的批次数据。
    - `output_size` (tuple): 模型输出特征图的尺寸 (H, W)。
    - `head_version` (str): 检测头版本，通常为 'full'。
    - `heatmap_channels` (int): 热力图通道数（1 或 2）。
- **输出说明**：
    - `dict`: 一个包含所有目标张量的字典，键包括 `heatmap_target`, `offset_target`, `center2vertex_target` 等。
- **节点流程可视化**:

```mermaid
flowchart TD
    A[开始] --> B(遍历批次中的每个样本);
    B --> C(提取中心点和顶点坐标);
    C --> D(根据 `output_size` 缩放坐标);
    D --> E(调用 `create_gaussian_heatmap_target` 生成热力图);
    E --> F{检查 `head_version` 是否为 'full'};
    F -- Y --> G(调用 `create_offset_target` 生成偏移目标);
    G --> H(调用 `create_center2vertex_target` 和 `create_vertex2center_target`);
    H --> I(将所有生成的目标张量存入字典);
    I --> J(堆叠批次中所有样本的目标);
    J --> K[返回目标字典];
    F -- N --> K;
    K --> L[结束];
```

### 子节点：`create_cycle_centernet_ms_loss`
- **文件路径**：`train-anything/networks/cycle_centernet_ms/cycle_centernet_loss_ms.py`
- **功能说明**：这是一个工厂函数，用于创建并配置 `CycleCenterNetLossMS` 损失函数实例。它接收各个损失分支的权重作为参数，并据此初始化 `CycleCenterNetLossMS`。该损失类内部组合了多种损失：
    1.  `GaussianFocalLossMS`：用于计算双通道热力图损失（中心点+顶点）。
    2.  `L1Loss`：用于计算偏移量、中心-顶点向量和顶点-中心向量的回归损失。
    3.  动态配对权重：在计算循环一致性损失（c2v, v2c）时，会根据预测和目标的差异动态计算一个权重。
- **输入参数**：
    - `heatmap_loss_weight` (float): 热力图损失的权重。
    - `reg_loss_weight` (float): 回归损失（偏移量）的权重。
    - `c2v_loss_weight` (float): 中心到顶点损失的权重。
    - `v2c_loss_weight` (float): 顶点到中心损失的权重。
- **输出说明**：
    - `CycleCenterNetLossMS`: 配置好的损失函数实例。
- **节点流程可视化**:

```mermaid
flowchart TD
    A[开始] --> B(接收各损失分支的权重);
    B --> C(创建 `heatmap_loss_cfg` 字典);
    C --> D(创建 `reg_loss_cfg`, `c2v_loss_cfg`, `v2c_loss_cfg` 字典);
    D --> E(使用配置字典实例化 `CycleCenterNetLossMS`);
    E --> F[返回 `CycleCenterNetLossMS` 实例];
    F --> G[结束];
```

### 子节点：`log_validation`
- **文件路径**：`train-anything/training_loops/table_structure_recognition/train_cycle_centernet_ms.py`
- **功能说明**：该函数负责执行周期性的模型验证。它在训练过程中被 `run_training_loop` 调用，用于评估模型在验证集上的性能。其核心功能包括：加载验证数据、执行模型前向传播、计算验证损失、记录评估指标，并根据配置触发可选的可视化流程。函数返回平均验证损失，用于判断是否为当前最佳模型。
- **输入参数**：
    - `config` (object): 训练配置对象，包含验证、EMA、可视化等相关参数。
    - `model` (torch.nn.Module): 待验证的模型。
    - `ema_handler` (EMAHandler): EMA处理器，用于加载EMA权重进行验证。
    - `global_step` (int): 当前的全局训练步数，用于日志记录。
    - `accelerator` (Accelerator): `accelerate` 库的中央控制器。
    - `weight_dtype` (torch.dtype): 模型权重的数据类型 (e.g., `torch.float16`)。
    - `val_loaders` (List[Tuple[str, DataLoader]]): 验证数据加载器列表。
- **输出说明**：
    - `avg_loss` (float): 在所有验证样本上计算出的平均损失值。
- **节点流程可视化**:

```mermaid
graph TD
    A[Start log_validation] --> B{val_loaders exist?};
    B -->|No| C["Return float('inf')"];
    B -->|Yes| D[Unwrap model];

    D --> E{EMA enabled?};
    E -->|Yes| F[Store original weights & Apply EMA weights];
    E -->|No| G[Set model to eval mode];
    F --> G;

    G --> H[Create CycleCenterNetLossMS criterion];
    H --> I[Initialize total_loss = 0.0, total_samples = 0];
    I --> J["Start iteration over val_loaders with torch.no_grad()"];

    J --> K[For each batch];
    K --> L[Move images to device];
    L --> M["Forward pass: predictions = model(images)"];
    M --> N[prepare_targets for batch];
    N --> O[Calculate avg_factor from target heatmap];
    O --> P["Calculate losses = criterion(predictions, targets, avg_factor)"];
    P --> Q[Compute total loss for the batch];
    Q --> R[Update total_loss and total_samples];
    R --> K;
    K --> S[End of validation loop];

    S --> T[Calculate avg_loss = total_loss / total_samples];
    T --> U["accelerator.log({'val_loss': avg_loss})"];

    U --> V{Visualization enabled?};
    V -->|Yes| W[Create TableStructureVisualizerMS];
    W --> X["visualizer.visualize_validation_samples(...)"];
    X --> Y{EMA enabled?};
    V -->|No| Y;

    Y -->|Yes| Z[Restore original model weights];
    Y -->|No| AA[Set model back to train mode];
    Z --> AA;

    AA --> BB[Return avg_loss];
    C --> End;
    BB --> End[End];
```

### 子节点：`save_best_checkpoints`
- **文件路径**：`train-anything/training_loops/table_structure_recognition/train_cycle_centernet_ms.py`
- **功能说明**：该函数负责根据验证结果保存最佳模型。它接收验证指标（在此场景下为平均损失），并与当前记录的最佳指标进行比较。如果当前模型的性能更优，它将更新记录文件 (`record.json`) 并调用 `save_state` 函数，将完整的模型状态（包括模型权重、EMA权重、优化器状态等）保存到指定的最佳模型目录中。
- **输入参数**：
    - `config`: (object) - 训练配置对象。
    - `accelerator`: (Accelerator) - `accelerate` 库的中央控制器。
    - `model`: (torch.nn.Module) - 待保存的模型。
    - `ema_handler`: (EMAHandler) - EMA处理器。
    - `optimizer`: (Optimizer) - 优化器。
    - `lr_scheduler`: (Scheduler) - 学习率调度器。
    - `global_step`: (int) - 当前的全局训练步数。
    - `val_metrics`: (float) - 从 `log_validation` 返回的验证指标（`avg_loss`）。
    - `record_dump_path`: (str) - 最佳模型记录文件 (`record.json`) 的路径。
    - `current_best_record`: (dict) - 内存中维护的当前最佳指标记录。
- **输出说明**：无。该函数执行文件I/O操作。
- **节点流程可视化**:

```mermaid
graph TD
    A[Start save_best_checkpoints] --> B["Receive val_metrics (avg_loss)"];
    B --> C{"avg_loss <= current_best_record['avg_loss']?"};
    C -->|No| D[End];
    C -->|Yes| E[found_best_model = True];
    E --> F[Update current_best_record in memory];
    F --> G["Write updated record to record.json"];
    G --> H[Call save_state to save checkpoint to best model directory];
    H --> D;
```


## 特别补充：

## 数据加载器
### 节点：`TableDataset` (类)
- **文件路径**：`train-anything/my_datasets/table_structure_recognition/table_dataset.py`
- **功能说明**：作为数据加载的核心，负责从磁盘读取图像和标注文件，解析标注内容，并将其转换为模型训练所需的格式。此类支持分布式标注（即每个图像一个标注文件）和多数据源加载。
- **核心方法**：
    - `__init__`: 初始化数据集。它会扫描指定的数据目录，通过 `_load_distributed_annotations` 加载所有合格样本的元数据（标注信息）。同时，它会实例化 `TableTransforms`，为数据增强做准备。
    - `_load_distributed_annotations`: 遍历数据目录，查找图像文件（`.jpg`, `.png`）及其对应的标注文件（`.json`）。它会读取标注文件，并只保留 `quality` 字段为 `"合格"` 的样本。
    - `__getitem__`: 数据集的核心方法。根据索引 `idx` 获取单个数据样本。它执行以下操作：
        1. 加载图像文件。
        2. 解析标注信息，提取每个单元格的边界框 (`bboxes`) 和中心点 (`cell_centers`)。
        3. 将数据打包成一个 `sample` 字典。
        4. 如果 `apply_transforms` 为 `True`，则调用 `TableTransforms` 对 `sample` 进行数据增强和预处理（如缩放、归一化等）。
        5. 返回处理后的 `sample`。
- **输入参数 (`__init__`)**:
    - `data_root` (Union[str, List[str]]): 一个或多个数据根目录。
    - `mode` (str): 'train' 或 'val'。
    - `apply_transforms` (bool): 是否应用数据变换。
    - 其他变换相关参数（`target_size`, `mean`, `std`）。
- **输出说明 (`__getitem__`)**：返回一个字典，包含预处理后的图像张量和对应的标签信息（边界框、中心点等）。

### 节点：`TableTransforms` (类)
- **文件路径**：`train-anything/my_datasets/table_structure_recognition/table_transforms.py`
- **功能说明**：定义并执行一个完整的数据预处理和增强流水线。该类的实例在 `TableDataset` 中被调用，对每个样本进行处理。
- **核心方法**：
    - `__call__`: 作为变换管道的入口，按顺序执行一系列操作：
        1. **颜色空间转换**：将 OpenCV 默认的 BGR 格式图像转换为 RGB。
        2. **数据增强**（仅在 `is_train=True` 时）：
            - `_apply_photometric_distortion`: 应用光度变换，如亮度、对比度调整。
            - `_apply_random_flip`: 执行随机水平翻转，并同步更新边界框和中心点坐标。
        3. **缩放**：调用 `_resize_image` 将图像调整到目标尺寸，并使用 `_scale_bboxes` 和 `_scale_points` 更新坐标。
        4. **归一化**：调用 `_normalize_image` 对图像进行标准化。
        5. **张量转换**：调用 `_to_tensor` 将 NumPy 数组转换为 PyTorch 张量。
- **输入参数 (`__call__`)**: 
    - `sample` (dict): 包含图像和标注的字典。
- **输出说明 (`__call__`)**: 返回一个更新后的 `sample` 字典，其中图像已转换为张量，坐标也已相应调整。

### 节点：`collate_fn` (函数)
- **文件路径**：`train-anything/my_datasets/table_structure_recognition/table_transforms.py`
- **功能说明**：作为 `DataLoader` 的一部分，此函数负责将一个批次（batch）中的多个独立样本（`sample` 字典列表）聚合成一个单一的批次字典。它将图像堆叠成一个四维张量，同时将其他可变长度的标注信息（如边界框）保留为列表。
- **输入参数**：
    - `batch` (List[Dict]): 一个批次的样本列表。
- **输出说明**：一个批次字典，包含一个批次的图像张量和其他标注信息列表。

### 节点：`create_cycle_centernet_ms_model` (函数)
- **文件路径**: `train-anything/networks/cycle_centernet_ms/cycle_centernet_model_ms.py`
- **功能说明**: 这是一个工厂函数，负责根据配置创建 `CycleCenterNetModelMS` 模型实例。它处理默认配置的合并，并可以加载 ModelScope 的预训练权重。
- **核心逻辑**:
    1. 定义并合并默认配置与用户传入的 `config`。
    2. 实例化 `CycleCenterNetModelMS` 类。
    3. 如果 `config` 中提供了 `checkpoint_path`，则调用模型的 `load_modelscope_weights` 方法加载权重。
- **输入参数**:
    - `config` (dict): 包含模型超参数（如 `base_name`, `pretrained`）和权重路径的字典。
- **输出说明**: 返回一个 `CycleCenterNetModelMS` 模型实例。

### 节点：`CycleCenterNetModelMS` (类)
- **文件路径**: `train-anything/networks/cycle_centernet_ms/cycle_centernet_model_ms.py`
- **功能说明**: 这是 Cycle-CenterNet 模型的顶层封装。它本身不实现复杂的网络逻辑，而是作为核心模型 `DLASegMS` 的容器。
- **核心逻辑**:
    - `__init__`: 初始化时，它会直接实例化 `DLASegMS` 类，后者包含了完整的骨干网络、上采样和检测头结构。
    - `forward`: 其前向传播方法直接调用内部 `self.model` (即 `DLASegMS` 实例) 的 `forward` 方法。
    - `load_modelscope_weights`: 提供了从磁盘加载预训练权重的功能。
- **下一关键调用**: `DLASegMS`

### 节点：`DLASegMS` (类)
- **文件路径**: `train-anything/networks/cycle_centernet_ms/cycle_centernet_head_ms.py`
- **功能说明**: 这是模型的核心实现，整合了骨干网络、上采样模块和多个检测头。
- **核心逻辑**:
    - `__init__`: 
        1. **实例化骨干网络**: 创建 `dla34` 实例作为特征提取器。
        2. **实例化上采样模块**: 创建 `DLAUp` 实例，用于融合和上采样来自骨干网络的多级特征。
        3. **动态创建检测头**: 根据 `self.heads` 字典的定义（`hm`, `v2c`, `c2v`, `reg`），为每个任务动态创建独立的卷积头（`nn.Sequential`）。
    - `forward`:
        1. 输入图像通过 `dla34` 骨干网络 (`self.base`)。
        2. 特征图通过 `DLAUp` 模块 (`self.dla_up`)。
        3. 上采样后的特征图被送入每个独立的检测头，生成最终的预测。
- **下一关键调用**: `dla34` (骨干网络) 和 `DLAUp` (上采样模块)。

### 节点：`dla34` (函数)
- **文件路径**: `train-anything/networks/cycle_centernet_ms/dla_backbone_ms.py`
- **功能说明**: 这是一个构造函数，专门用于创建 DLA-34 架构的实例。它通过调用通用的 `DLA` 类并传入预设的配置参数（层级、通道数和 `BasicBlock`）来完成。
- **核心逻辑**: 直接实例化 `DLA` 类。
- **下一关键调用**: `DLA` (通用骨干网络类)。

### 节点：`DLA` (类)
- **文件路径**: `train-anything/networks/cycle_centernet_ms/dla_backbone_ms.py`
- **功能说明**: DLA（Deep Layer Aggregation）网络的通用实现。它通过聚合不同层级的特征来构建一个强大的特征提取器。
- **核心逻辑**:
    - `__init__`: 按照 DLA 的架构，依次构建 `level0` 到 `level5` 的网络层。其中，`level2` 到 `level5` 使用了核心的 `Tree` 结构来对特征进行迭代式的深度聚合。
    - `forward`: 输入的图像依次通过各个层级。当 `return_levels=True` 时，它会返回一个包含各层级输出特征图的列表，供后续模块使用。
- **下一关键调用**: `Tree` (DLA的核心组件)。


## 损失函数 (Loss Function)

### 节点：`create_cycle_centernet_ms_loss` (函数)
- **文件路径**: `train-anything/networks/cycle_centernet_ms/cycle_centernet_loss_ms.py`
- **功能说明**: 创建并配置 `CycleCenterNetLossMS` 损失函数实例的工厂函数。
- **核心逻辑**:
    - 接收各个损失分量的权重作为参数。
    - 创建配置字典，并用其初始化 `CycleCenterNetLossMS` 类。
- **下一关键调用**: `CycleCenterNetLossMS`。

### 节点：`CycleCenterNetLossMS` (类)
- **文件路径**: `train-anything/networks/cycle_centernet_ms/cycle_centernet_loss_ms.py`
- **功能说明**: Cycle-CenterNet 的组合损失函数，集成了多个独立的损失计算模块。
- **核心逻辑**:
    - `__init__`: 初始化四个核心的损失函数：
        - `heatmap_loss`: `GaussianFocalLossMS`，用于计算双通道热力图的 Focal Loss。
        - `reg_loss`: `L1Loss`，用于计算中心点偏移的 L1 损失。
        - `c2v_loss`: `L1Loss`，用于计算中心到顶点的 L1 损失。
        - `v2c_loss`: `L1Loss`，用于计算顶点到中心的 L1 损失。
    - `forward`: 接收模型预测和真实标签，分别调用上述四个损失函数，并返回一个包含所有损失分量的字典。
- **下一关键调用**: `GaussianFocalLossMS` 和 `L1Loss` (具体的损失计算实现)。

### 节点：`GaussianFocalLossMS` (类)
- **文件路径**: `train-anything/networks/cycle_centernet_ms/cycle_centernet_loss_ms.py`
- **功能说明**: Focal Loss 的一种变体，专门用于监督由高斯函数生成的热力图目标。
- **核心逻辑**:
    - `forward`: 实现了 Focal Loss 的核心计算公式 `-(1-p)^γ * log(p)`。它能够处理 “软” 的高斯目标，并通过 `alpha` 和 `gamma` 参数来调整正负样本的权重，使模型专注于学习困难样本。

### 节点：`L1Loss` (类)
- **文件路径**: `train-anything/networks/cycle_centernet/cycle_centernet_loss.py`
- **功能说明**: 一个通用的 L1 损失函数封装，用于各种回归任务。
- **核心逻辑**:
    - `forward`: 封装了 `torch.nn.functional.l1_loss`，并增加了对样本权重（`weight`）和自定义平均因子（`avg_factor`）的支持。

### 节点：`get_optimizer` (函数)
- **文件路径**: `train-anything/modules/utils/train_tools.py`
- **功能说明**: 一个通用的优化器工厂函数，负责根据配置创建和恢复优化器。
- **核心逻辑**:
    - **类型选择**: 根据配置文件中的 `optimizer.type` (如 `SGD`, `Adam`, `AdamW`)，选择并实例化相应的 PyTorch 优化器类。
    - **参数配置**: 从配置文件中读取学习率、betas、权重衰减等超参数，并用于初始化优化器。
    - **状态恢复**: 如果提供了优化器检查点 (`optimizer_ckpt`)，则会加载其 `state_dict`，从而能够从中断处无缝恢复训练。
    - **8-bit 支持**: 支持 `bitsandbytes` 库的 8-bit Adam 优化器，可显著减少显存占用。

### 节点：`get_scheduler` (函数)
- **文件路径**: `train-anything/modules/utils/optimization.py`
- **功能说明**: 一个通用的学习率调度器（Learning Rate Scheduler）工厂函数，用于创建和配置学习率调整策略。
- **核心逻辑**:
    - **类型选择**: 根据配置文件中的 `scheduler.name`（如 `cosine`, `linear`, `constant_with_warmup`），从一个预定义的映射 `TYPE_TO_SCHEDULER_FUNCTION` 中查找并选择相应的学习率调整函数。
    - **参数配置**: 接收 `num_warmup_steps`（预热步数）和 `num_training_steps`（总训练步数）等关键参数，这些参数通常在 `setup_training_components` 中计算得出。
    - **返回类型**: 所有调度器最终都封装在 PyTorch 的 `LambdaLR` 中返回，这是一个非常灵活的调度器，它通过一个自定义的 lambda 函数来调整学习率。
    - **功能丰富**: 支持多种常见的学习率策略，如余弦退火、线性衰减、多项式衰减和分段常数等。



## 整体用途

本文档旨在深度剖析 `train_cycle_centernet_ms.py` 脚本的完整端到端训练流程。该脚本基于 `accelerate` 框架，用于训练一个针对表格结构识别（Table Structure Recognition, TSR）任务的 Cycle-CenterNet 模型（ModelScope 版本）。

分析将从配置文件解析开始，贯穿数据加载、模型构建、训练循环、验证评估，直至最终模型保存的每一个环节。通过梳理核心模块、关键函数及其交互关系，为代码理解、二次开发和模型迁移提供清晰、可靠的参考依据。

## 目录结构

根据入口文件 `train_cycle_centernet_ms.py` 及其配置文件，梳理出核心的目录结构和依赖关系如下。这对于理解代码组织和规划迁移路径至关重要。

```bash
# /path/to/your/project/ (项目根目录)
|
|-- train-anything/
|   |
|   |-- configs/                                   # 配置文件根目录
|   |   `-- table_structure_recognition/
|   |       `-- cycle_centernet/
|   |           `-- cycle_centernet_ms_config.yaml # (入口) 本次分析的核心配置文件
|   |
|   |-- modules/                                   # 可复用的通用模块
|   |   |-- proj_cmd_args/
|   |   |   `-- cycle_centernet/
|   |   |       `-- args.py                        # (依赖) 解析 YAML 配置和命令行参数
|   |   |-- utils/
|   |   |   |-- log.py                             # (依赖) 文件日志记录器
|   |   |   |-- optimization.py                  # (依赖) 定义 get_scheduler
|   |   |   |-- torch_utils.py                     # (依赖) PyTorch 相关工具（如EMA）
|   |   |   `-- train_tools.py                   # (依赖) 训练工具（get_optimizer, save_state等）
|   |   `-- visualization/
|   |       `-- table_structure_visualizer_ms.py # (依赖) 验证过程中的可视化工具
|   |       `-- dry_run_visualizer.py            # (依赖) 干运行过程中的可视化工具
|   |
|   |-- my_datasets/
|   |   `-- table_structure_recognition/         # 表格结构识别数据集模块
|   |       |-- __init__.py
|   |       |-- table_dataset.py                   # (依赖) TableDataset 核心实现
|   |       |-- table_transforms.py                # (依赖) TableTransforms 数据增强实现
|   |       `-- target_preparation.py              # (依赖) prepare_targets 核心实现
|   |
|   |-- networks/
|   |   |-- cycle_centernet/                     # 原始 Cycle-CenterNet 模块
|   |   |   `-- cycle_centernet_loss.py          # (依赖) L1Loss 定义于此
|   |   `-- cycle_centernet_ms/                  # ModelScope 版本的模型和损失
|   |       |-- __init__.py
|   |       |-- cycle_centernet_head_ms.py
|   |       |-- cycle_centernet_loss_ms.py       # (依赖) CycleCenterNetLossMS 和 GaussianFocalLossMS
|   |       |-- cycle_centernet_model_ms.py      # (依赖) CycleCenterNetModelMS 和 DLASegMS
|   |       `-- dla_backbone_ms.py               # (依赖) DLA-34 骨干网络 (DLA, Tree, Root, DLAUp, IDAUp)
|   |
|   `-- training_loops/
|       `-- table_structure_recognition/
|           `-- train_cycle_centernet_ms.py      # (入口) 本次分析的核心训练脚本
|
|-- data/                                          # (外部) 存放训练/验证数据的根目录 (示例)
|   `-- TabRecSet_chinese/
|       |-- train/
|       `-- val/
|
`-- output/                                        # (外部) 存放训练输出的根目录 (示例)
    `-- cycle-centernet-ms/
        |-- checkpoints/                           # 保存的模型检查点
        `-- logs/                                  # 保存的日志文件
```

## 调用时序图

以下时序图概括了 `train_cycle_centernet_ms.py` 脚本的核心执行流程和组件交互顺序。

```mermaid
sequenceDiagram
    participant User
    participant Script as train_cycle_centernet_ms.py
    participant Accelerator
    participant Dataloaders
    participant Model
    participant Loss

    User->>Script: 执行训练命令
    activate Script

    Script->>Script: main(): 解析配置, 准备环境
    Script->>Accelerator: prepare_training_enviornment_v2()
    Script->>Dataloaders: prepare_dataloaders()
    Script->>Model: create_model_and_ema()
    Script->>Loss: setup_training_components()
    Script->>Accelerator: prepare_accelerator_components()

    loop 训练循环 (Epochs)
        loop 步数循环 (Steps)
            Script->>Dataloaders: 获取批次数据
            Dataloaders-->>Script: 返回 batch
            Script->>Model: 前向传播: model(batch)
            Model-->>Script: 返回 predictions
            Script->>Loss: 计算损失: loss_criterion(predictions, targets)
            Loss-->>Script: 返回 loss
            Script->>Accelerator: 反向传播: backward(loss)
            Script->>Accelerator: 更新权重: optimizer.step()
        end

        alt 定期验证
            Script->>Script: log_validation()
            loop 验证循环
                 Script->>Dataloaders: 获取验证数据
                 Script->>Model: model(val_batch)
                 Script->>Loss: 计算验证损失
            end
            Script->>Script: save_best_checkpoints()
        end
    end

    Script->>Script: save_final_model()
    deactivate Script

```

## 实体关系图

下图展示了训练流程中核心实体（类）之间的关系。

```mermaid
classDiagram
    class train_cycle_centernet_ms {
        +main()
        +run_training_loop()
        +log_validation()
        +save_best_checkpoints()
    }

    class Accelerator {
        +prepare(...)
        +backward(loss)
    }

    class CycleCenterNetMSModel {
        +forward(images)
    }

    class CycleCenterNetLossMS {
        +forward(predictions, targets)
    }

    class TableDataset {
        -root_dir: str
        -anno_file: str
        -transforms: TableTransforms
        +__getitem__(idx)
        +__len__()
    }

    class TableTransforms {
        +__call__(data)
    }
    
    class DataLoader {
        +dataset: TableDataset
    }

    train_cycle_centernet_ms --|> Accelerator : uses
    train_cycle_centernet_ms --|> CycleCenterNetMSModel : creates & uses
    train_cycle_centernet_ms --|> CycleCenterNetLossMS : creates & uses
    train_cycle_centernet_ms --|> DataLoader : creates & uses
    
    DataLoader "1" -- "1" TableDataset : wraps
    TableDataset "1" -- "1" TableTransforms : uses

```

