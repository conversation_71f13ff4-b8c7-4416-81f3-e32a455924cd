#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025-01-03 11:00
# <AUTHOR> <EMAIL>
# @FileName: dla_backbone.py

"""
DLA-34骨干网络实现

基于原Cycle-CenterNet项目中的DLANetMMDet3D实现，
适配train-anything项目的架构要求。
"""

import os
import urllib.request

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Tuple, Optional, Dict, Any
from tqdm import tqdm


def download_with_progress(url: str, local_path: str):
    """
    带进度条的文件下载函数

    Args:
        url: 下载URL
        local_path: 本地保存路径
    """
    try:
        # 获取文件大小
        with urllib.request.urlopen(url) as response:
            total_size = int(response.headers.get('Content-Length', 0))

        # 创建进度条
        progress_bar = tqdm(
            total=total_size,
            unit='B',
            unit_scale=True,
            unit_divisor=1024,
            desc=f"Downloading {os.path.basename(local_path)}"
        )

        def progress_hook(block_num, block_size, total_size):
            """进度回调函数"""
            downloaded = block_num * block_size
            if downloaded <= total_size:
                progress_bar.update(block_size)
            else:
                # 处理最后一个块可能超出总大小的情况
                remaining = total_size - progress_bar.n
                if remaining > 0:
                    progress_bar.update(remaining)

        # 下载文件
        urllib.request.urlretrieve(url, local_path, reporthook=progress_hook)
        progress_bar.close()

        print(f"✅ Download completed: {local_path}")

    except Exception as e:
        if 'progress_bar' in locals():
            progress_bar.close()
        print(f"❌ Download failed: {e}")
        raise


class BasicBlock(nn.Module):
    """
    DLA网络中的基础残差块
    
    Args:
        in_channels: 输入通道数
        out_channels: 输出通道数
        stride: 卷积步长
        dilation: 膨胀率
    """
    
    def __init__(
        self,
        in_channels: int,
        out_channels: int,
        stride: int = 1,
        dilation: int = 1
    ):
        super(BasicBlock, self).__init__()
        
        self.conv1 = nn.Conv2d(
            in_channels,
            out_channels,
            kernel_size=3,
            stride=stride,
            padding=dilation,
            dilation=dilation,
            bias=False
        )
        self.bn1 = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU(inplace=True)
        
        self.conv2 = nn.Conv2d(
            out_channels,
            out_channels,
            kernel_size=3,
            stride=1,
            padding=dilation,
            dilation=dilation,
            bias=False
        )
        self.bn2 = nn.BatchNorm2d(out_channels)
        self.stride = stride
    
    def forward(self, x: torch.Tensor, identity: Optional[torch.Tensor] = None) -> torch.Tensor:
        """前向传播"""
        if identity is None:
            identity = x
        
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)
        
        out = self.conv2(out)
        out = self.bn2(out)
        
        out += identity
        out = self.relu(out)
        
        return out


class Root(nn.Module):
    """
    DLA网络中的根节点模块，用于特征聚合
    
    Args:
        in_channels: 输入通道数
        out_channels: 输出通道数
        kernel_size: 卷积核大小
        add_identity: 是否添加恒等连接
    """
    
    def __init__(
        self,
        in_channels: int,
        out_channels: int,
        kernel_size: int = 1,
        add_identity: bool = False
    ):
        super(Root, self).__init__()
        
        self.conv = nn.Conv2d(
            in_channels,
            out_channels,
            kernel_size=kernel_size,
            stride=1,
            padding=(kernel_size - 1) // 2,
            bias=False
        )
        self.bn = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU(inplace=True)
        self.add_identity = add_identity
    
    def forward(self, feat_list: List[torch.Tensor]) -> torch.Tensor:
        """
        前向传播
        
        Args:
            feat_list: 特征列表
            
        Returns:
            聚合后的特征
        """
        children = feat_list
        x = self.conv(torch.cat(feat_list, 1))
        x = self.bn(x)
        
        if self.add_identity:
            x += children[0]
        
        x = self.relu(x)
        return x


class Tree(nn.Module):
    """
    DLA网络中的树结构模块，实现分层特征聚合
    
    Args:
        levels: 树的层数
        block: 基础块类型
        in_channels: 输入通道数
        out_channels: 输出通道数
        stride: 步长
        level_root: 是否为层级根节点
        root_dim: 根节点维度
        root_kernel_size: 根节点卷积核大小
        dilation: 膨胀率
        root_residual: 根节点是否使用残差连接
    """
    
    def __init__(
        self,
        levels: int,
        block: nn.Module,
        in_channels: int,
        out_channels: int,
        stride: int = 1,
        level_root: bool = False,
        root_dim: int = 0,
        root_kernel_size: int = 1,
        dilation: int = 1,
        root_residual: bool = False
    ):
        super(Tree, self).__init__()
        
        if root_dim == 0:
            root_dim = 2 * out_channels
        
        if level_root:
            root_dim += in_channels
        
        if levels == 1:
            self.tree1 = block(in_channels, out_channels, stride, dilation=dilation)
            self.tree2 = block(out_channels, out_channels, 1, dilation=dilation)
        else:
            self.tree1 = Tree(
                levels - 1, block, in_channels, out_channels,
                stride, root_dim=0, root_kernel_size=root_kernel_size,
                dilation=dilation, root_residual=root_residual
            )
            self.tree2 = Tree(
                levels - 1, block, out_channels, out_channels,
                root_dim=root_dim + out_channels, root_kernel_size=root_kernel_size,
                dilation=dilation, root_residual=root_residual
            )
        
        if levels == 1:
            self.root = Root(root_dim, out_channels, root_kernel_size, root_residual)
        
        self.level_root = level_root
        self.root_dim = root_dim
        self.downsample = None
        self.project = None
        self.levels = levels
        
        if stride > 1:
            self.downsample = nn.MaxPool2d(stride, stride=stride)
        
        if in_channels != out_channels:
            self.project = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, kernel_size=1, stride=1, bias=False),
                nn.BatchNorm2d(out_channels)
            )
    
    def forward(
        self, 
        x: torch.Tensor, 
        identity: Optional[torch.Tensor] = None, 
        children: Optional[List[torch.Tensor]] = None
    ) -> torch.Tensor:
        """前向传播"""
        children = [] if children is None else children
        bottom = self.downsample(x) if self.downsample else x
        identity = self.project(bottom) if self.project else bottom
        
        if self.level_root:
            children.append(bottom)
        
        x1 = self.tree1(x, identity)
        
        if self.levels == 1:
            x2 = self.tree2(x1)
            feat_list = [x2, x1] + children
            x = self.root(feat_list)
        else:
            children.append(x1)
            x = self.tree2(x1, children=children)
        
        return x


class DLA34Backbone(nn.Module):
    """
    DLA-34骨干网络
    
    Args:
        in_channels: 输入通道数，默认为3（RGB图像）
        out_indices: 输出特征层的索引
        frozen_stages: 冻结的阶段数
        pretrained: 预训练模型路径
    """
    
    # DLA-34的架构配置：(block, levels, channels)
    arch_settings = {
        34: (BasicBlock, [1, 1, 1, 2, 2, 1], [16, 32, 64, 128, 256, 512])
    }
    
    def __init__(
        self,
        depth: int = 34,
        in_channels: int = 3,
        out_indices: Tuple[int, ...] = (0, 1, 2, 3, 4, 5),
        frozen_stages: int = -1,
        layer_with_level_root: Tuple[bool, ...] = (False, True, True, True),
        with_identity_root: bool = False,
        pretrained: Optional[str] = None
    ):
        super(DLA34Backbone, self).__init__()
        
        if depth not in self.arch_settings:
            raise KeyError(f"Invalid depth {depth} for DLA")
        
        block, levels, channels = self.arch_settings[depth]
        self.channels = channels
        self.num_levels = len(levels)
        self.frozen_stages = frozen_stages
        self.out_indices = out_indices
        self.pretrained = pretrained
        
        assert max(out_indices) < self.num_levels
        
        # 基础层：7x7卷积 + BN + ReLU
        self.base_layer = nn.Sequential(
            nn.Conv2d(in_channels, channels[0], kernel_size=7, stride=1, padding=3, bias=False),
            nn.BatchNorm2d(channels[0]),
            nn.ReLU(inplace=True)
        )
        
        # 前两层使用普通卷积层
        for i in range(2):
            level_layer = self._make_conv_level(
                channels[0] if i == 0 else channels[i-1],
                channels[i],
                levels[i],
                stride=i + 1
            )
            layer_name = f"level{i}"
            self.add_module(layer_name, level_layer)
        
        # 后续层使用Tree结构
        for i in range(2, self.num_levels):
            dla_layer = Tree(
                levels[i],
                block,
                channels[i - 1],
                channels[i],
                stride=2,
                level_root=layer_with_level_root[i - 2],
                root_residual=with_identity_root
            )
            layer_name = f"level{i}"
            self.add_module(layer_name, dla_layer)
        
        self._freeze_stages()
    
    def _make_conv_level(
        self, 
        in_channels: int, 
        out_channels: int, 
        num_convs: int, 
        stride: int = 1, 
        dilation: int = 1
    ) -> nn.Sequential:
        """构建卷积层级"""
        modules = []
        for i in range(num_convs):
            modules.extend([
                nn.Conv2d(
                    in_channels if i == 0 else out_channels,
                    out_channels,
                    kernel_size=3,
                    stride=stride if i == 0 else 1,
                    padding=dilation,
                    bias=False,
                    dilation=dilation
                ),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True)
            ])
        return nn.Sequential(*modules)
    
    def _freeze_stages(self):
        """冻结指定阶段的参数"""
        if self.frozen_stages >= 0:
            self.base_layer.eval()
            for param in self.base_layer.parameters():
                param.requires_grad = False
        
        for i in range(1, self.frozen_stages + 1):
            if i < self.num_levels:
                layer = getattr(self, f"level{i}")
                layer.eval()
                for param in layer.parameters():
                    param.requires_grad = False
    
    def forward(self, x: torch.Tensor) -> List[torch.Tensor]:
        """
        前向传播
        
        Args:
            x: 输入图像张量，形状为 (N, C, H, W)
            
        Returns:
            多尺度特征列表
        """
        outs = []
        x = self.base_layer(x)
        
        for i in range(self.num_levels):
            level_layer = getattr(self, f"level{i}")
            x = level_layer(x)
            if i in self.out_indices:
                outs.append(x)
        
        return outs
    
    def train(self, mode: bool = True):
        """设置训练模式"""
        super(DLA34Backbone, self).train(mode)
        self._freeze_stages()
        return self

    def init_weights(self):
        """初始化网络权重"""
        # 如果指定了预训练模型，加载预训练权重
        if self.pretrained:
            self._load_pretrained_weights(self.pretrained)
        else:
            # 默认权重初始化
            for m in self.modules():
                if isinstance(m, nn.Conv2d):
                    nn.init.normal_(m.weight, std=0.001)
                    if m.bias is not None:
                        nn.init.constant_(m.bias, 0)
                elif isinstance(m, nn.BatchNorm2d):
                    nn.init.constant_(m.weight, 1)
                    nn.init.constant_(m.bias, 0)

    def _load_pretrained_weights(self, pretrained_path: str):
        """
        加载预训练权重

        Args:
            pretrained_path: 预训练权重路径，支持本地文件或URL
        """
        try:
            # 如果是URL，先下载到本地
            if pretrained_path.startswith('http'):
                # 创建缓存目录 - 先尝试获取 TORCH_HOME 环境变量
                torch_home = os.environ.get('TORCH_HOME')
                if torch_home:
                    cache_dir = os.path.join(torch_home, 'hub', 'checkpoints')
                else:
                    cache_dir = os.path.expanduser('~/.cache/torch/hub/checkpoints')
                os.makedirs(cache_dir, exist_ok=True)

                # 生成本地文件名
                filename = os.path.basename(pretrained_path)
                if '%2B' in filename:
                    filename = filename.replace('%2B', '+')
                local_path = os.path.join(cache_dir, filename)

                # 如果本地文件不存在，下载
                if not os.path.exists(local_path):
                    print(f"🔄 Downloading pretrained weights from {pretrained_path}")
                    download_with_progress(pretrained_path, local_path)

                pretrained_path = local_path

            # 加载权重
            print(f"Loading pretrained weights from {pretrained_path}")
            checkpoint = torch.load(pretrained_path, map_location='cpu')

            # 处理不同的权重格式
            if 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            elif 'model' in checkpoint:
                state_dict = checkpoint['model']
            else:
                state_dict = checkpoint

            # 过滤不匹配的键
            model_state_dict = self.state_dict()
            filtered_state_dict = {}

            for k, v in state_dict.items():
                # 移除可能的前缀
                key = k
                if key.startswith('backbone.'):
                    key = key[9:]  # 移除 'backbone.' 前缀
                if key.startswith('module.'):
                    key = key[7:]   # 移除 'module.' 前缀

                if key in model_state_dict and v.shape == model_state_dict[key].shape:
                    filtered_state_dict[key] = v
                else:
                    print(f"Skipping key {k} -> {key} due to shape mismatch or missing key")

            # 加载过滤后的权重
            missing_keys, unexpected_keys = self.load_state_dict(filtered_state_dict, strict=False)

            if missing_keys:
                print(f"Missing keys: {missing_keys}")
            if unexpected_keys:
                print(f"Unexpected keys: {unexpected_keys}")

            print(f"Successfully loaded pretrained weights. "
                  f"Loaded {len(filtered_state_dict)}/{len(model_state_dict)} parameters.")

        except Exception as e:
            print(f"Failed to load pretrained weights from {pretrained_path}: {e}")
            print("Falling back to default initialization")
