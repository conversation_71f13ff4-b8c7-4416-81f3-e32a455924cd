#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025-01-03 16:00
# <AUTHOR> <EMAIL>
# @FileName: cycle_centernet_model.py

"""
Cycle-CenterNet完整模型定义

组合DLA-34骨干网络和检测头，提供完整的Cycle-CenterNet模型实现。
"""

from typing import Tuple, Union, List

import torch
import torch.nn as nn

from .dla_backbone import DLA34Backbone
from .ct_resnet_neck import CTResNetNeck
from .cycle_centernet_head import CycleCenterNetHead


class CycleCenterNetModel(nn.Module):
    """
    Cycle-CenterNet模型
    
    组合DLA-34骨干网络、CTResNetNeck颈部网络和CycleCenterNet检测头。
    
    Args:
        backbone_depth: DLA骨干网络深度，默认34
        head_feat_channels: 检测头特征通道数，默认64
        num_classes: 类别数量，默认1（表格单元格）
        head_version: 检测头版本，'simple'或'full'
        backbone_out_indices: 骨干网络输出层索引，默认(2,)表示level2
        backbone_in_channels: 骨干网络输入通道数，默认3
    """
    
    def __init__(
        self,
        backbone_depth: int = 34,
        head_feat_channels: int = 64,
        num_classes: int = 1,
        head_version: str = "full",
        backbone_out_indices: Tuple[int, ...] = (5,),
        backbone_in_channels: int = 3,
        backbone_pretrained: str = None,
        use_neck: bool = True,
        neck_in_channel: int = 512,
        neck_deconv_filters: Tuple[int, ...] = (256, 128, 64),
        neck_deconv_kernels: Tuple[int, ...] = (4, 4, 4),
        neck_use_dcn: bool = True
    ):
        super(CycleCenterNetModel, self).__init__()
        
        self.head_version = head_version
        self.backbone_depth = backbone_depth
        self.num_classes = num_classes
        self.use_neck = use_neck

        # DLA-34骨干网络
        self.backbone = DLA34Backbone(
            depth=backbone_depth,
            in_channels=backbone_in_channels,
            out_indices=backbone_out_indices,
            pretrained=backbone_pretrained
        )
        self.backbone.init_weights()

        # CTResNetNeck颈部网络
        if use_neck:
            self.neck = CTResNetNeck(
                in_channel=neck_in_channel,
                num_deconv_filters=neck_deconv_filters,
                num_deconv_kernels=neck_deconv_kernels,
                use_dcn=neck_use_dcn
            )
            # neck输出通道数是最后一个deconv_filter的通道数
            head_input_channels = neck_deconv_filters[-1]
        else:
            self.neck = None
            # 获取骨干网络输出通道数
            # level5的输出通道数为512（DLA-34架构）
            if backbone_out_indices == (5,):
                head_input_channels = 512
            else:
                # 其他层的通道数需要根据DLA架构确定
                head_input_channels = 512  # 默认值，可以根据需要调整
        
        # 检测头（只支持完整版）
        if head_version == "full":
            self.head = CycleCenterNetHead(
                in_channel=head_input_channels,
                feat_channel=head_feat_channels,
                num_classes=num_classes
            )
        else:
            raise ValueError(f"Unsupported head version: {head_version}. "
                             f"Only 'full' version is supported.")
    
    def forward(self, images: torch.Tensor) -> Union[Tuple[torch.Tensor], Tuple[torch.Tensor, ...]]:
        """
        前向传播
        
        Args:
            images: 输入图像，形状为 (N, 3, H, W)
            
        Returns:
            预测结果元组：
            - simple版本: (heatmap,)
            - full版本: (heatmap, offset, center2vertex, vertex2center)
        """
        # 骨干网络特征提取
        features = self.backbone(images)

        # 颈部网络特征融合（如果使用）
        if self.neck is not None:
            features = self.neck(features)

        # 检测头预测
        predictions = self.head(features)

        return predictions
    
    def get_model_info(self) -> dict:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        total_params = sum(p.numel() for p in self.parameters())
        trainable_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        return {
            'model_name': 'CycleCenterNet',
            'backbone_depth': self.backbone_depth,
            'head_version': self.head_version,
            'num_classes': self.num_classes,
            'total_params': total_params,
            'trainable_params': trainable_params,
            'backbone_params': sum(p.numel() for p in self.backbone.parameters()),
            'head_params': sum(p.numel() for p in self.head.parameters())
        }


def create_cycle_centernet_model(
    backbone_depth: int = 34,
    head_feat_channels: int = 64,
    num_classes: int = 1,
    head_version: str = "full",
    backbone_pretrained: str = None,
    **kwargs
) -> CycleCenterNetModel:
    """
    创建Cycle-CenterNet模型的工厂函数
    
    Args:
        backbone_depth: DLA骨干网络深度
        head_feat_channels: 检测头特征通道数
        num_classes: 类别数量
        head_version: 检测头版本
        **kwargs: 其他参数
        
    Returns:
        CycleCenterNet模型实例
    """
    model = CycleCenterNetModel(
        backbone_depth=backbone_depth,
        head_feat_channels=head_feat_channels,
        num_classes=num_classes,
        head_version=head_version,
        backbone_pretrained=backbone_pretrained,
        **kwargs
    )
    
    return model


# 预定义的模型配置
MODEL_CONFIGS = {
    'cycle_centernet_default': {
        'backbone_depth': 34,
        'head_feat_channels': 64,
        'num_classes': 2,  # 背景 + 单元格
        'head_version': 'full'
    },
    'cycle_centernet_table_cell': {
        'backbone_depth': 34,
        'head_feat_channels': 64,
        'num_classes': 2,  # 背景 + 表格单元格
        'head_version': 'full'
    }
}


def create_model_from_config(config_name: str, **override_kwargs) -> CycleCenterNetModel:
    """
    从预定义配置创建模型
    
    Args:
        config_name: 配置名称
        **override_kwargs: 覆盖参数
        
    Returns:
        CycleCenterNet模型实例
    """
    if config_name not in MODEL_CONFIGS:
        raise ValueError(f"Unknown config: {config_name}. "
                        f"Available configs: {list(MODEL_CONFIGS.keys())}")
    
    config = MODEL_CONFIGS[config_name].copy()
    config.update(override_kwargs)
    
    return create_cycle_centernet_model(**config)


# 便捷函数
def cycle_centernet_default(**kwargs) -> CycleCenterNetModel:
    """创建默认配置的Cycle-CenterNet模型"""
    return create_model_from_config('cycle_centernet_default', **kwargs)


def cycle_centernet_table_cell(**kwargs) -> CycleCenterNetModel:
    """创建表格单元格检测的Cycle-CenterNet模型"""
    return create_model_from_config('cycle_centernet_table_cell', **kwargs)
