#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025-01-04 (重构版本)
# <AUTHOR> <EMAIL>
# @FileName: args.py

"""
Cycle-CenterNet表格结构识别训练参数解析 (重构版本)

基于OmegaConf的配置文件驱动方式，支持命令行参数覆盖。

使用示例:
    # 基本使用
    python train_cycle_centernet.py -c configs/cycle_centernet_config.yaml

    # 覆盖特定参数 (支持多种格式)
    # 格式1: 多个-o参数
    python train_cycle_centernet.py \\
        -c configs/cycle_centernet_config.yaml \\
        -o training.epochs=100 \\
        -o training.batch_size=4 \\
        -o data.paths.train_data_dir=/path/to/data

    # 格式2: 单个-o后跟多个参数
    python train_cycle_centernet.py \\
        -c configs/cycle_centernet_config.yaml \\
        -o training.epochs=100 training.batch_size=4 data.paths.train_data_dir=/path/to/data

    # 调试模式
    python train_cycle_centernet.py \\
        -c configs/cycle_centernet_config.yaml \\
        -o basic.debug=true

    # 只执行可视化（不训练不验证）
    python train_cycle_centernet.py \\
        -c configs/cycle_centernet_config.yaml \\
        -o basic.only_vis_log=true
"""

import sys
from .config_parser import load_config


def preprocess_args(args=None):
    """
    预处理命令行参数，支持灵活的 -o 参数格式
    
    将原始命令行参数转换为标准的 argparse 格式。
    支持在单个 -o 后跟多个 key=value 参数。
    
    Args:
        args: 命令行参数列表，默认使用 sys.argv[1:]
        
    Returns:
        list: 预处理后的参数列表
    """
    if args is None:
        args = sys.argv[1:]
    
    processed_args = []
    i = 0
    
    while i < len(args):
        arg = args[i]
        
        if arg in ["-o", "--override"]:
            # 遇到 -o 参数
            processed_args.append(arg)
            i += 1
            
            # 收集后续的所有 key=value 参数
            override_values = []
            
            while i < len(args):
                next_arg = args[i]
                
                # 如果是新的选项参数（以-开头且不是key=value格式），停止收集
                if next_arg.startswith('-') and '=' not in next_arg:
                    break
                    
                # 如果包含=号，认为是 key=value 格式
                if '=' in next_arg:
                    override_values.append(next_arg)
                    i += 1
                else:
                    # 不是 key=value 格式，停止收集
                    break
            
            # 将收集到的参数合并为一个字符串（用空格分隔）
            if override_values:
                combined_value = ' '.join(override_values)
                processed_args.append(combined_value)
            else:
                # 没有找到任何覆盖参数，报错
                raise ValueError(f"Missing value for {arg} option")
                
        else:
            # 非 -o 参数，直接添加
            processed_args.append(arg)
            i += 1
    
    return processed_args


def parse_args():
    """
    解析参数并返回配置对象
    
    这是新的简化版本，直接返回层级配置对象而不是扁平化的args。
    训练脚本应该直接使用config.xxx.xxx的方式访问配置。

    Returns:
        OmegaConf DictConfig: 层级配置对象
    """
    import argparse
    
    # 预处理命令行参数
    processed_args = preprocess_args()
    
    parser = argparse.ArgumentParser(
        description="Cycle-CenterNet表格结构识别训练",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 基本使用
  python train_cycle_centernet.py -c configs/cycle_centernet_config.yaml
  
  # 覆盖参数 - 格式1: 多个-o参数
  python train_cycle_centernet.py -c configs/cycle_centernet_config.yaml -o training.epochs=100 -o training.batch_size=4
  
  # 覆盖参数 - 格式2: 单个-o后跟多个参数
  python train_cycle_centernet.py -c configs/cycle_centernet_config.yaml -o training.epochs=100 training.batch_size=4

  # 只执行可视化（不训练不验证）
  python train_cycle_centernet.py -c configs/cycle_centernet_config.yaml -o basic.only_vis_log=true
        """
    )
    
    parser.add_argument(
        "-c", "--config", 
        type=str, 
        required=True,
        help="配置文件路径"
    )
    
    parser.add_argument(
        "-o", "--override",
        action="append",
        default=[],
        help="覆盖配置参数，格式: key=value (可多次使用，或在单个-o后跟多个参数)"
    )

    # 使用预处理后的参数进行解析
    args = parser.parse_args(processed_args)

    # 加载配置
    config = load_config(args.config, args.override)

    return config
