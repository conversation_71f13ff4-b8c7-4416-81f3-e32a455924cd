```markdown
# LORE-TSR 迁移至 Train-Anything 框架需求规划 (PRD)

**版本:** 1.0

**核心目标:** 将表格结构识别（TSR）项目 `LORE-TSR` 从其原生的、基于自定义脚本的架构，完整、平滑地迁移至现代化、高可扩展性的 `train-anything` 训练框架。本次迁移旨在利用 `train-anything` 的先进特性（如 `Accelerate` 分布式训练、`OmegaConf` 配置管理、标准化的数据处理流程），优化开发效率和模型训练性能，同时确保 `LORE-TSR` 核心算法的逻辑和精度不受影响。

**迁移原则:**

- **小步快跑，迭代验证:** 将复杂的迁移任务分解为一系列独立的、可测试、可交付的迭代，确保每个阶段都有明确的产出和验证标准。
- **逻辑平移，保证复现性:** 迁移过程中，严格保持 `LORE-TSR` 核心模型、损失函数和关键预处理的初始定义和内部逻辑不变，以验证迁移后算法效果的可复现性。
- **拥抱框架，而非改造:** 充分利用 `train-anything` 的设计哲学和工具链，将 `LORE-TSR` 的功能适配到框架中，而不是反向修改框架以适应旧代码。

---

### **总览：迭代计划 (Roadmap)**

整个迁移过程将分为五个核心迭代，其中第三个迭代（模型与损失函数）将进一步细分为两个子步骤，确保每个阶段的专注和可验证性。

- **Iteration 1 (MVP):** 配置系统迁移与标准化
- **Iteration 2:** 数据集适配与兼容
- **Iteration 3.1:** 模型封装与工厂化
- **Iteration 3.2:** 损失函数封装与工厂化
- **Iteration 4:** 训练流程重构与 `Accelerate` 集成
- **Iteration 5:** 文档完善与清理
- **Iteration 6:** 端到端测试与最终交付

---

### **详细迭代需求**

#### **Iteration 1 (MVP): 配置系统迁移 (已深化)**

- **需求 (Requirements):**
    1.  **创建配置文件**: 创建 `LORE-TSR` 专用的层级化 `YAML` 配置文件，路径为 `configs/table_structure_recognition/lore_tsr/lore_tsr_ms_config.yaml`。
    2.  **参数迁移**: 将 `LORE-TSR/src/lib/opts.py` 中定义的所有**非数据相关**的参数（如模型架构、学习率、优化器、损失权重等）迁移至 `YAML` 文件中，并保留原始超参数值。
    3.  **设计 `data` 配置节**: 针对数据适配进行专门设计，以取代 `opts.py` 中分散的数据相关参数。此配置节必须包含：
        -   `paths`: 用于定义训练和验证数据集的路径，兼容 `train-anything` 的分布式目录结构。
        -   `loader`: 定义数据加载器参数，如 `num_workers`、`pin_memory`。
        -   `processing`: 定义数据处理和增强参数，必须包含：
            -   `input_res`: `LORE-TSR` 的输入图像分辨率。
            -   `mean`, `std`: `LORE-TSR` 使用的图像归一化参数。
            -   `augmentation`: 一个显式的子节点，用于完整复现 `LORE-TSR` 的数据增强策略，如 `rand_crop`, `color_jitter`, `scale`, `shift`, `rotate` 等。
    4.  **支持命令行覆写**: 确保 `train-anything` 框架的配置加载机制能解析新的 `YAML` 文件，并支持通过命令行 `-o` (overwrite) 动态覆写任意配置项。

- **产出 (Deliverables):**
    1.  一个结构清晰、符合上述设计要求的 `configs/table_structure_recognition/lore_tsr/lore_tsr_ms_config.yaml` 文件。
    2.  （可能无需修改）确保现有的配置解析代码能够正常工作。

- **验收标准 (Acceptance Criteria):**
    1.  配置文件包含了 `opts.py` 的所有核心参数，特别是 `data` 部分的结构符合新设计。
    2.  可以通过脚本加载配置，并能成功访问 `data.processing.augmentation.scale` 等深度嵌套的参数。
    3.  可以通过命令行覆写至少一个数据增强相关的配置项。

---

#### **Iteration 2: 数据集适配 (已深化)**

- **需求 (Requirements):**
    1.  **创建适配器类**: 在 `my_datasets/table_structure_recognition/` 目录下，创建新的 `LoreTSRTableDataset` 类，该类继承自 `train-anything` 的 `TableDataset`。
    2.  **复用数据查找**: 完全复用父类 `TableDataset` 的初始化逻辑，使其能够自动处理分布式数据目录的遍历和图像/标注文件的配对。
    3.  **重构核心处理逻辑**: 重写 `__getitem__` 方法，使其成为连接新旧体系的桥梁：
        a.  在方法内部，首先调用父类的逻辑加载原始图像和JSON标注。
        b.  读取 **Iteration 1** 中新定义的 `cfg.data.processing.augmentation` 配置。
        c.  根据配置，调用从 `LORE-TSR` 中剥离并重构的、纯粹的数据增强函数，对图像和标注执行变换。
        d.  调用从 `LORE-TSR` 中剥离的核心 `prepare_targets` 逻辑，根据增强后的数据生成模型训练所需的热图、偏移等目标。
    4.  **保持兼容**: 适配器必须消费 `train-anything` 的标准分布式JSON格式，不得引入任何旧格式的依赖。
    5.  **质量过滤**: 沿用 `train-anything` 的设计，在加载时检查标注质量，并静默跳过不合格样本。

- **产出 (Deliverables):**
    1.  `my_datasets/table_structure_recognition/lore_tsr_dataset.py` (或类似命名) 文件，包含 `LoreTSRTableDataset` 类的完整定义。
    2.  （可能需要）`my_datasets/table_structure_recognition/lore_transforms.py` 中新增的、从 `LORE-TSR` 迁移过来的数据增强函数。

- **验收标准 (Acceptance Criteria):**
    1.  使用新的数据集类和 **Iteration 1** 创建的配置文件，能够成功实例化一个 `DataLoader`。
    2.  能够从 `DataLoader` 中取出一个批次（batch）的数据，通过可视化或断言，确认数据增强（如旋转、缩放）已按配置生效。
    3.  批次中 `targets` 的结构、`shape` 和 `dtype` 与 `LORE-TSR` 原始输出完全一致。

---

#### **Iteration 3.1: 模型封装**

- **需求 (Requirements):**
    1.  创建新目录 `networks/lore_tsr/`。
    2.  在该目录下，定义一个新的模型类 `LORETSRModel(nn.Module)`，将原 `LORE-TSR` 的 `model` (检测器) 和 `Processor` (Transformer) 两个独立的模块合并为一个单一、集成的模型。
    3.  `LORETSRModel` 的 `forward` 方法需清晰地体现两阶段逻辑：`detector_features = self.detector(x)`，然后 `logical_outputs = self.processor(detector_features)`。
    4.  `LORETSRModel` 的构造函数 (`__init__`) 必须能够根据 `YAML` 配置文件中的参数（如 `model.backbone`），动态创建和加载不同的检测器子模块（如 `dla_34`, `resfpn_18` 等）。
    5.  提供一个模型工厂函数 `create_lore_tsr_model(cfg)`，根据传入的配置返回一个实例化的 `LORETSRModel`。
    6.  **约束:** 封装过程不得改变 `DLA`、`ResNet-DCN`、`Transformer` 等核心组件的内部网络结构和计算逻辑。

- **产出 (Deliverables):**
    1.  `networks/lore_tsr/model.py` 文件，包含 `LORETSRModel` 的定义。
    2.  `networks/lore_tsr/factory.py` 文件（或合并在 `model.py` 中），包含 `create_lore_tsr_model` 工厂函数。

- **验收标准 (Acceptance Criteria):**
    1.  能够通过工厂函数成功创建一个 `LORETSRModel` 实例。
    2.  将一个 `torch.randn` 的模拟输入传递给模型，能够顺利完成前向传播并得到输出，且输出的 `shape` 符合预期。
    3.  更改配置文件中的 `model.backbone` 参数，能够成功创建出使用不同主干网络的模型实例。

---

#### **Iteration 3.2: 损失函数封装**

- **需求 (Requirements):**
    1.  在 `networks/lore_tsr/` 目录下，定义一个新的损失类 `LORETSRLoss`。
    2.  将原 `LORE-TSR` 项目中 `CtdetLoss` 的完整计算逻辑（包括热图损失、回归损失、结构损失等）迁移到 `LORETSRLoss` 类中。
    3.  `LORETSRLoss` 的 `forward` 方法应接收模型输出和数据标签，返回一个包含总损失（用于反向传播）和各项子损失（用于日志记录）的字典。
    4.  提供一个损失函数工厂 `create_lore_tsr_loss(cfg)`，根据配置返回 `LORETSRLoss` 的实例。
    5.  **约束:** 封装过程不得改变任何损失函数的具体数学计算方式和权重系数。

- **产出 (Deliverables):**
    1.  `networks/lore_tsr/loss.py` 文件，包含 `LORETSRLoss` 的定义和 `create_lore_tsr_loss` 工厂函数。

- **验收标准 (Acceptance Criteria):**
    1.  能够通过工厂函数成功创建一个 `LORETSRLoss` 实例。
    2.  使用模拟的模型输出和数据标签，能够成功调用损失函数并计算出总损失值（一个标量 `torch.Tensor`）。

---

#### **Iteration 4: 训练脚本与流程重构 (架构对齐版)**

- **目标 (Goal):** 复用 `train-anything` 框架中现代化的训练流程，创建一个新的训练脚本 `train_lore_tsr_ms.py`，并将其与我们前面迭代中创建的 `LORE-TSR` 专属模块（配置、数据、模型、损失）无缝集成。

- **需求 (Requirements):**
    1.  **创建训练脚本**: 复制 `train-anything/training_loops/table_structure_recognition/train_cycle_centernet_ms.py` 为 `train_lore_tsr_ms.py`。
    2.  **核心组件替换**: 在新脚本中，将所有对 `cycle_centernet` 的模型、损失、数据集的创建和调用，全部替换为 `LORE-TSR` 的对应模块 (`LoreTSRTableDataset`, `create_lore_tsr_model`, `create_lore_tsr_loss`)。
    3.  **实现目标准备逻辑 (Target Preparation)**:
        -   在 `my_datasets/table_structure_recognition/` 目录下创建 `lore_tsr_utils.py`。
        -   在该文件中定义 `lore_tsr_prepare_targets` 函数，其职责是将 `LoreTSRTableDataset` 输出的 `batch` 数据，转换为 `LORETSRLoss` 所需的 `(images, targets)` 标准格式。
        -   在 `train_lore_tsr_ms.py` 的训练循环中，每次迭代时调用此函数。
    4.  **实现独立的验证流程 (Validation)**:
        -   在 `train_lore_tsr_ms.py` 中，创建一个独立的、与主训练循环解耦的 `lore_tsr_log_validation` 函数。
        -   此函数将作为验证流程的入口，负责调用评估器和可视化器，并记录日志。
    5.  **实现评估器 (Evaluator)**:
        -   在 `modules/evaluation/` 目录下创建 `lore_tsr_evaluator.py`，其中包含 `LoreTSREvaluator` 类。
        -   **职责**: 接收模型原始输出 `preds`，执行解码（decode）和核心指标计算（如 TEDS），返回解码后的结果和评估指标字典。
    6.  **实现可视化器 (Visualizer)**:
        -   在 `modules/visualization/` 目录下创建 `lore_tsr_visualizer.py`，其中包含 `visualize_lore_tsr_output` 函数。
        -   **职责**: 接收 `LoreTSREvaluator` 解码后的结果和原始图像，生成与 `LORE-TSR` 风格一致的可视化图片，并将其交由 `accelerator` 记录。

- **产出 (Deliverables):**
    1.  `training_loops/table_structure_recognition/train_lore_tsr_ms.py` (主训练脚本)
    2.  `my_datasets/table_structure_recognition/lore_tsr_utils.py` (包含 `lore_tsr_prepare_targets`)
    3.  `modules/evaluation/lore_tsr_evaluator.py` (包含 `LoreTSREvaluator`)
    4.  `modules/visualization/lore_tsr_visualizer.py` (包含 `visualize_lore_tsr_output`)

- **验收标准 (Acceptance Criteria):**
    1.  能够通过 `accelerate launch` 成功启动 `train_lore_tsr_ms.py` 并开始训练。
    2.  训练和验证的损失（loss）能够正常记录到控制台和 TensorBoard。
    3.  验证阶段能够计算并记录 `LORE-TSR` 的核心评估指标。
    4.  验证阶段能够生成与 `LORE-TSR` 风格一致的可视化结果图并保存。
    5.  模型检查点能够被正确保存和加载。

---

#### **Iteration 5: 文档与清理**

- **目标 (Goal):** 提供清晰、完整的使用文档，并清理项目中的冗余文件，确保项目的整洁和可维护性。

- **需求 (Requirements):**
    1.  **创建 README**: 在项目根目录下创建一个 `README_LORE_TSR.md` 文件。
    2.  **编写环境配置说明**: 在 README 中，详细说明如何配置 `conda` 环境，并特别强调 `DCNv2` 和 `NMS` 的手动编译步骤和注意事项。
    3.  **编写训练与推理说明**: 提供清晰的单行命令，说明如何使用 `accelerate launch` 启动训练，以及如何运行推理脚本。
    4.  **提供配置文件示例**: 在 README 中，链接到 `lore_tsr_config.yaml`，并对关键配置项（如数据路径、batch size、学习率）进行简要说明。
    5.  **代码清理**: 删除迁移过程中产生的临时文件、测试脚本和不再需要的旧模块。

- **产出 (Deliverables):**
    1.  `README_LORE_TSR.md`

- **验收标准 (Acceptance Criteria):**
    1. 文档内容清晰、准确，无歧义。
    2. 一个不熟悉本项目的新成员，能够根据 README 独立完成环境配置、编译、训练和推理的全过程。

---

#### **Iteration 6: 端到端测试与最终交付**

- **目标 (Goal):** 进行一次完整的端到端测试，确保迁移后的项目在性能和精度上与原始 `LORE-TSR` 项目对齐，并正式交付。

- **需求 (Requirements):**
    1.  **精度对齐测试**: 使用完全相同的配置（数据集、超参数），分别在原始 `LORE-TSR` 项目和我们迁移后的项目中进行训练。
    2.  **性能对比**: 对比两个项目的最终评估指标，确保迁移后项目的精度没有出现不可接受的下降。
    3.  **最终代码审查**: 对所有新代码进行最后一次审查，确保代码风格统一、注释清晰。

- **产出 (Deliverables):**
    1.  一份简单的精度对比报告（可以记录在 `README_LORE_TSR.md` 中）。
    2.  一个稳定、可交付的、完全集成到 `train-anything` 框架中的 `LORE-TSR` 项目。

- **验收标准 (Acceptance Criteria):**
    1.  迁移后项目的核心评估指标与原始项目相比，处于可接受的误差范围内。
    2.  项目代码整洁，文档清晰，达到交付标准。

- **需求 (Requirements):**
    1.  创建新的训练入口脚本 `training_loops/table_structure_recognition/train_lore_tsr_ms.py`。
    2.  该脚本必须使用 HuggingFace `accelerate` 库来处理设备分配（CPU/GPU/DP/DDP）、模型和数据加载器的准备 (`accelerator.prepare`) 以及反向传播 (`accelerator.backward`)。
    3.  脚本的执行流程应为：初始化 `accelerator` -> 加载配置 -> 创建数据集和 `DataLoader` -> 创建模型、损失函数、优化器 -> 调用 `accelerator.prepare` -> 进入训练主循环。
    4.  训练循环中需实现完整的训练、验证、模型检查点保存和日志记录（如 `wandb` 或 `tensorboard`）逻辑。
    5.  **约束:** 必须完全替换 `LORE-TSR` 原有的 `src/main.py` 和 `src/lib/trains/base_trainer.py` 的逻辑，不得保留任何旧的训练流程代码。

- **产出 (Deliverables):**
    1.  `training_loops/table_structure_recognition/train_lore_tsr_ms.py` 脚本。

- **验收标准 (Acceptance Criteria):**
    1.  执行该脚本，能够在单GPU上无报错地跑通至少一个完整的训练和验证 `epoch`。
    2.  训练过程中，损失值能够正常下降。
    3.  指定的模型保存路径下成功生成了模型检查点文件 (`.pth`)。

---


```
