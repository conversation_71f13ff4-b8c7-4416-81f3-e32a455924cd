
## 动态迁移蓝图 (V1.2)

### 1. 文件迁移映射表

我们已成功完成编译依赖的复制，现在将状态更新为 `已完成`。

| 源文件 (LORE-TSR)                                                                                     | 目标文件 (train-anything) | 迁移策略 (说明) | 状态 |
|:---------------------------------------------------------------------------------------------------| :--- | :--- | :--- |
| **入口与配置**                                                                                          | | | |
| `src/main.py`                                                                                      | `training_loops/table_structure_recognition/train_lore_tsr.py` | **重构**: 以 `cycle-centernet-ms` 为模板，适配 `accelerate` 训练循环。 | `未开始` |
| `src/lib/opts.py`                                                                                  | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | **重构**: 转换为 `OmegaConf` 的 YAML 格式。 | `未开始` |
| **数据集**                                                                                            | | | |
| `src/lib/datasets/dataset_factory.py`                                                              | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | **重构**: 创建新的 `lore_tsr_dataset.py`。 | `未开始` |
| `src/lib/datasets/table_dataset/table.py`                                                          | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | **重构**: 核心逻辑将被整合进 `lore_tsr_dataset.py`。 | `未开始` |
| **训练器/检测器**                                                                                        | | | |
| `src/lib/detectors/base_detector.py`                                                               | (由框架取代) | **废弃**: 由 `training_loop` 取代。 | `未开始` |
| `src/lib/detectors/ctdet.py`                                                                       | `training_loops/table_structure_recognition/train_lore_tsr.py` | **重构**: 核心 `run_epoch` 逻辑将被迁移。 | `未开始` |
| **核心模型与算法**                                                                                        | | | |
| `src/lib/models/model.py`                                                                          | `networks/lore_tsr/model_factory.py` | **复制与适配**: `create_model` 逻辑将被复制和适配。 | `未开始` |
| `src/lib/models/networks/pose_dla_dcn.py`（在`models.py`中导入后有别名`dla`, 模型工厂创建依赖的其他模型都如此——具有别名，需要进行识别） | `networks/lore_tsr/pose_dla_dcn.py` | **复制**: 核心骨干网络。 | `进行中` |
| `src/lib/models/losses.py`                                                                         | `networks/lore_tsr/losses.py` | **复制**: 核心损失函数。 | `进行中` |
| `src/lib/utils/post_process.py`                                                                    | `networks/lore_tsr/post_process.py` | **复制**: 核心后处理逻辑。 | `进行中` |
| **编译依赖**                                                                                           | | | |
| `src/lib/external/DCNv2/`                                                                          | `external/lore_tsr/DCNv2/` | **复制与隔离**: 复制源代码。 | `已完成` |
| `src/lib/external/nms.pyx`                                                                         | `external/lore_tsr/nms.pyx` | **复制与隔离**: 复制源代码。 | `已完成` |

### 2. 逻辑依赖与迁移路径图 (Mermaid)

此步骤将开始填充 `networks/lore_tsr/` 目录，遵循“复制并保留核心算法”的黄金法则。

```mermaid
graph TD
    %% ------------------- Source: LORE-TSR -------------------
    subgraph "Source: LORE-TSR"
        direction LR
        S_model("src/lib/models/model.py")
        S_dla("src/lib/models/networks/pose_dla_dcn.py")
        S_losses("src/lib/models/losses.py")
        S_post_process("src/lib/utils/post_process.py")
        S_dcn("src/lib/external/DCNv2/")
    end

    %% ------------------- Target: train-anything -------------------
    subgraph "Target: train-anything"
        direction LR
        T_model_factory("networks/lore_tsr/model_factory.py")
        T_dla("networks/lore_tsr/pose_dla_dcn.py")
        T_losses("networks/lore_tsr/losses.py")
        T_post_process("networks/lore_tsr/post_process.py")
        T_dcn("external/lore_tsr/DCNv2/")
    end

    %% ------------------- Mappings & Dependencies -------------------
    S_dla -- "Copy" --> T_dla
    S_losses -- "Copy" --> T_losses
    S_post_process -- "Copy" --> T_post_process

    S_model -- "Copy & Adapt" --> T_model_factory
    
    T_dla -.-> T_dcn
    T_model_factory -.-> T_dla
    T_model_factory -.-> T_losses
    T_model_factory -.-> T_post_process
```

### 3. 目标目录结构树

`external` 目录下的文件已从 `[placeholder]` 变为实际存在。

```text
train-anything/
├── configs/
│   └── table_structure_recognition/
│       └── lore_tsr/
│           └── lore_tsr_config.yaml  [placeholder]
├── external/
│   └── lore_tsr/
│       ├── DCNv2/
│       └── nms.pyx
├── my_datasets/
│   └── table_structure_recognition/
│       └── lore_tsr_dataset.py     [placeholder]
├── networks/
│   └── lore_tsr/
│       ├── pose_dla_dcn.py         [placeholder]
│       ├── losses.py               [placeholder]
│       ├── model_factory.py        [placeholder]
│       └── post_process.py         [placeholder]
└── training_loops/
    └── table_structure_recognition/
        └── train_lore_tsr.py       [placeholder]
```

---

## 编码计划：Step 3

**步骤 3.1: 复制核心算法模块 (骨干网络、损失函数、后处理)**

**目标:** 遵循迁移黄金法则第一条，将实现核心算法的文件（不包括模型创建/加载的工厂文件）近乎逐字地复制到 `train-anything` 的 `networks/lore_tsr/` 目录中。这一步迁移了模型的计算核心，是后续适配和重构的基础。

**影响文件:**
*   `train-anything/networks/lore_tsr/pose_dla_dcn.py` (创建并填充)
*   `train-anything/networks/lore_tsr/losses.py` (创建并填充)
*   `train-anything/networks/lore_tsr/post_process.py` (创建并填充)

**具体操作:**
1.  **复制 `pose_dla_dcn.py`:**
    *   源路径: `LORE-TSR/src/lib/models/networks/pose_dla_dcn.py`
    *   目标路径: `train-anything/networks/lore_tsr/pose_dla_dcn.py`
    *   执行 `cp LORE-TSR/src/lib/models/networks/pose_dla_dcn.py train-anything/networks/lore_tsr/pose_dla_dcn.py` 命令。
2.  **复制 `losses.py`:**
    *   源路径: `LORE-TSR/src/lib/models/losses.py`
    *   目标路径: `train-anything/networks/lore_tsr/losses.py`
    *   执行 `cp LORE-TSR/src/lib/models/losses.py train-anything/networks/lore_tsr/losses.py` 命令。
3.  **复制 `post_process.py`:**
    *   源路径: `LORE-TSR/src/lib/utils/post_process.py`
    *   目标路径: `train-anything/networks/lore_tsr/post_process.py`
    *   执行 `cp LORE-TSR/src/lib/utils/post_process.py train-anything/networks/lore_tsr/post_process.py` 命令。

**如何验证 (Verification):**
*   执行以下 `shell` 命令，检查目标文件是否已成功创建。

```shell
# 在 train-anything 项目的父目录下运行
ls -l train-anything/networks/lore_tsr/
```

*   **预期输出:**
    *   命令的输出中应包含 `dla.py`, `losses.py`, 和 `post_process.py` 三个文件。
    *   如果任一文件缺失，则表示步骤失败。

---
