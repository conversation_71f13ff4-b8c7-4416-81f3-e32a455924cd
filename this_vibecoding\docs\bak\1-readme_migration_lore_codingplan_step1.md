# LORE-TSR 迁移编码计划 - 动态演进版

**架构师:** Cascade
**状态:** 初始计划生成
**日期:** 2025-07-14

---

## 动态迁移蓝图 (The Dynamic Blueprint)

### 1. 文件迁移映射表 (File Migration Map)

| 源文件 (LORE-TSR) | 目标文件 (train-anything) | 迁移策略 (说明) | 状态 |
| :--- | :--- | :--- | :--- |
| `src/main.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | **重构:** 废弃原有流程，基于 `Accelerate` 框架重写训练主循环。 | `未开始` |
| `src/lib/opts.py` | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | **转换:** 从 `argparse` 转换为 `OmegaConf` 的 YAML 文件，实现配置解耦。 | `未开始` |
| `src/lib/opts.py` | `modules/proj_cmd_args/lore_tsr/args.py` | **新建:** 创建新的配置加载和解析模块。 | `未开始` |
| `src/lib/datasets/dataset_factory.py` | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | **适配:** 适配 `train-anything` 的 `TableDataset` 接口。 | `未开始` |
| `src/lib/datasets/sample/ctdet.py` | `my_datasets/table_structure_recognition/lore_tsr_transforms.py` | **重构:** 将数据增强和目标生成逻辑封装为 `TableTransforms`。 | `未开始` |
| `src/lib/models/model.py` | `networks/lore_tsr/model.py` | **封装:** 将模型创建逻辑封装为 `create_lore_tsr_model` 工厂函数。 | `未开始` |
| `src/lib/models/networks/` | `networks/lore_tsr/` | **平移/封装:** 迁移核心网络结构 (DLA, Transformer)，并作为 `LORETSRModel` 的子模块。 | `未开始` |
| `src/lib/trains/ctdet.py` (CtdetLoss) | `networks/lore_tsr/loss.py` | **封装:** 将复杂的损失计算逻辑封装为 `LORETSRLoss` 类和工厂函数。 | `未开始` |
| `src/lib/trains/base_trainer.py` | (被废弃) | **废弃:** 训练器逻辑由 `train_lore_tsr.py` 中的 `Accelerate` 循环替代。 | `不适用` |
| `src/lib/external/` | `networks/lore_tsr/external/` | **平移:** 迁移需要手动编译的源码 (NMS, DCNv2)，并提供编译说明。 | `未开始` |
| (N/A) | `training_loops/table_structure_recognition/lore_tsr/README.md` | **新建:** 创建详细的文档，说明配置、运行方法和特殊依赖。 | `未开始` |


### 2. 目标目录结构树 (Target Directory Tree)

```text
train-anything/
├── configs/
│   └── table_structure_recognition/
│       └── lore_tsr/                                           [empty]
│           └── lore_tsr_config.yaml                          [placeholder]
├── modules/
│   └── proj_cmd_args/
│       └── lore_tsr/                                           [empty]
│           └── args.py                                       [placeholder]
├── my_datasets/
│   └── table_structure_recognition/
│       └── lore_tsr_dataset.py                               [placeholder]
│       └── lore_tsr_transforms.py                            [placeholder]
├── networks/
│   └── lore_tsr/
│       ├── external/                                           [empty]
│       ├── __init__.py                                       [placeholder]
│       ├── model.py                                          [placeholder]
│       └── loss.py                                           [placeholder]
└── training_loops/
    └── table_structure_recognition/
        ├── lore_tsr/                                           [empty]
        │   └── README.md                                     [placeholder]
        └── train_lore_tsr.py                                   [placeholder]
```

---

## 编码计划：第 1 步

### **步骤 1.1: 创建项目骨架目录**

- **步骤目标:** 建立 `LORE-TSR` 迁移所需的基础目录结构。一个清晰的、预先定义的目录结构是确保后续代码和模块能够被有序地、无歧义地放置的基础。这是所有迁移工作的第一步。

- **影响文件 (均为新建目录):**
  - `train-anything/configs/table_structure_recognition/lore_tsr/`
  - `train-anything/modules/proj_cmd_args/lore_tsr/`
  - `train-anything/networks/lore_tsr/external/`
  - `train-anything/training_loops/table_structure_recognition/lore_tsr/`

- **具体操作:**
  - 在 `train-anything` 项目的相应位置，创建上述所有空目录。

- **如何验证 (Verification):**
  - 运行以下 `shell` 命令，如果没有任何 `No such file or directory` 的错误信息，则证明所有目录均已成功创建。

  ```shell
  ls -d train-anything/configs/table_structure_recognition/lore_tsr/ \
  train-anything/modules/proj_cmd_args/lore_tsr/ \
  train-anything/networks/lore_tsr/external/ \
  train-anything/training_loops/table_structure_recognition/lore_tsr/
  ```
