#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025-01-03 10:30
# <AUTHOR> <EMAIL>
# @FileName: table_transforms.py

import random
from typing import Dict, Any, Tuple, List, Optional

import cv2
import torch
import numpy as np
from PIL import Image


class TableTransforms:
    """
    表格数据预处理和变换管道
    
    包含图像归一化、随机裁剪、缩放、数据增强等功能
    """
    
    def __init__(
        self,
        target_size: Tuple[int, int] = (512, 512),
        mean: List[float] = [0.485, 0.456, 0.406],
        std: List[float] = [0.229, 0.224, 0.225],
        to_rgb: bool = True,
        is_train: bool = True
    ):
        """
        初始化变换管道
        
        Args:
            target_size: 目标图像尺寸 (height, width)
            mean: 归一化均值
            std: 归一化标准差
            to_rgb: 是否转换为RGB格式
            is_train: 是否为训练模式
        """
        self.target_size = target_size
        self.mean = np.array(mean, dtype=np.float32)
        self.std = np.array(std, dtype=np.float32)
        self.to_rgb = to_rgb
        self.is_train = is_train
    
    def __call__(self, sample: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用变换管道
        
        Args:
            sample: 输入样本
            
        Returns:
            变换后的样本
        """
        image = sample['image']
        bboxes = sample['bboxes']
        labels = sample['labels']
        cell_centers = sample['cell_centers']
        
        # 获取原始图像尺寸
        orig_h, orig_w = image.shape[:2]
        
        # 颜色空间转换
        if self.to_rgb and image.shape[2] == 3:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 训练时应用数据增强
        if self.is_train:
            # 光度变换
            image = self._apply_photometric_distortion(image)
            
            # 随机翻转
            image, bboxes, cell_centers = self._apply_random_flip(image, bboxes, cell_centers)
        
        # 缩放到目标尺寸
        image, scale_factor = self._resize_image(image, self.target_size)
        
        # 更新坐标
        if len(bboxes) > 0:
            bboxes = self._scale_bboxes(bboxes, scale_factor)
            cell_centers = self._scale_points(cell_centers, scale_factor)
        
        # 归一化
        image = self._normalize_image(image)
        
        # 转换为张量格式
        image = self._to_tensor(image)
        
        # 更新样本
        sample.update({
            'image': image,
            'bboxes': bboxes,
            'labels': labels,
            'cell_centers': cell_centers,
            'scale_factor': scale_factor,
            'orig_size': (orig_h, orig_w),
            'img_shape': self.target_size
        })
        
        return sample
    
    def _apply_photometric_distortion(self, image: np.ndarray) -> np.ndarray:
        """
        应用光度变换（亮度、对比度、饱和度、色调）
        
        Args:
            image: 输入图像
            
        Returns:
            变换后的图像
        """
        image = image.astype(np.float32)
        
        # 亮度变换
        if random.random() < 0.5:
            delta = random.uniform(-32, 32)
            image += delta
        
        # 对比度变换
        if random.random() < 0.5:
            alpha = random.uniform(0.5, 1.5)
            image *= alpha
        
        # 转换为HSV进行饱和度和色调变换
        if image.shape[2] == 3:
            image = np.clip(image, 0, 255).astype(np.uint8)
            hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV).astype(np.float32)
            
            # 饱和度变换
            if random.random() < 0.5:
                hsv[:, :, 1] *= random.uniform(0.5, 1.5)
            
            # 色调变换
            if random.random() < 0.5:
                hsv[:, :, 0] += random.uniform(-18, 18)
                hsv[:, :, 0] = np.clip(hsv[:, :, 0], 0, 179)
            
            hsv = np.clip(hsv, 0, 255).astype(np.uint8)
            image = cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB).astype(np.float32)
        
        return np.clip(image, 0, 255)
    
    def _apply_random_flip(
        self, 
        image: np.ndarray, 
        bboxes: np.ndarray, 
        cell_centers: np.ndarray
    ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        应用随机翻转
        
        Args:
            image: 输入图像
            bboxes: 边界框
            cell_centers: 单元格中心点
            
        Returns:
            翻转后的图像、边界框和中心点
        """
        if random.random() < 0.5:  # 水平翻转
            image = cv2.flip(image, 1)
            h, w = image.shape[:2]
            
            if len(bboxes) > 0:
                # 翻转边界框
                bboxes_flipped = bboxes.copy()
                bboxes_flipped[:, [0, 2]] = w - bboxes[:, [2, 0]]
                bboxes = bboxes_flipped
                
                # 翻转中心点
                cell_centers_flipped = cell_centers.copy()
                cell_centers_flipped[:, 0] = w - cell_centers[:, 0]
                cell_centers = cell_centers_flipped
        
        return image, bboxes, cell_centers
    
    def _resize_image(self, image: np.ndarray, target_size: Tuple[int, int]) -> Tuple[np.ndarray, Tuple[float, float]]:
        """
        缩放图像到目标尺寸
        
        Args:
            image: 输入图像
            target_size: 目标尺寸 (height, width)
            
        Returns:
            缩放后的图像和缩放因子 (scale_x, scale_y)
        """
        h, w = image.shape[:2]
        target_h, target_w = target_size
        
        # 计算缩放因子
        scale_x = target_w / w
        scale_y = target_h / h
        
        # 缩放图像
        resized_image = cv2.resize(image, (target_w, target_h), interpolation=cv2.INTER_LINEAR)
        
        return resized_image, (scale_x, scale_y)
    
    def _scale_bboxes(self, bboxes: np.ndarray, scale_factor: Tuple[float, float]) -> np.ndarray:
        """
        缩放边界框坐标
        
        Args:
            bboxes: 边界框数组，形状为 (N, 4)
            scale_factor: 缩放因子 (scale_x, scale_y)
            
        Returns:
            缩放后的边界框
        """
        if len(bboxes) == 0:
            return bboxes
        
        scale_x, scale_y = scale_factor
        scaled_bboxes = bboxes.copy()
        scaled_bboxes[:, [0, 2]] *= scale_x  # x坐标
        scaled_bboxes[:, [1, 3]] *= scale_y  # y坐标
        
        return scaled_bboxes
    
    def _scale_points(self, points: np.ndarray, scale_factor: Tuple[float, float]) -> np.ndarray:
        """
        缩放点坐标
        
        Args:
            points: 点坐标数组，形状为 (N, 2)
            scale_factor: 缩放因子 (scale_x, scale_y)
            
        Returns:
            缩放后的点坐标
        """
        if len(points) == 0:
            return points
        
        scale_x, scale_y = scale_factor
        scaled_points = points.copy()
        scaled_points[:, 0] *= scale_x  # x坐标
        scaled_points[:, 1] *= scale_y  # y坐标
        
        return scaled_points
    
    def _normalize_image(self, image: np.ndarray) -> np.ndarray:
        """
        归一化图像

        Args:
            image: 输入图像 (0-255范围)

        Returns:
            归一化后的图像 (ImageNet标准化后的浮点值)
        """
        # 将图像从0-255范围转换为0-1范围进行归一化
        image_01 = image.astype(np.float32) / 255.0
        # 应用ImageNet标准化
        image_normalized = (image_01 - self.mean) / self.std
        # 返回归一化后的浮点值，不再转换回0-255范围
        return image_normalized
    
    def _to_tensor(self, image: np.ndarray) -> torch.Tensor:
        """
        将图像转换为PyTorch张量

        Args:
            image: 输入图像，形状为 (H, W, C)，已经归一化的浮点值

        Returns:
            张量，形状为 (C, H, W)，归一化后的浮点值
        """
        # 图像已经是归一化后的浮点值，直接转换维度顺序
        # 转换维度顺序：(H, W, C) -> (C, H, W)
        image_tensor = np.transpose(image, (2, 0, 1))
        return torch.from_numpy(image_tensor.copy())


def collate_fn(batch: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    批处理函数，将多个样本组合成一个批次
    
    Args:
        batch: 样本列表
        
    Returns:
        批次数据
    """
    # 提取各个字段
    images = []
    bboxes_list = []
    labels_list = []
    cell_centers_list = []
    image_ids = []
    
    for sample in batch:
        images.append(sample['image'])
        bboxes_list.append(sample['bboxes'])
        labels_list.append(sample['labels'])
        cell_centers_list.append(sample['cell_centers'])
        image_ids.append(sample['image_id'])
    
    # 堆叠图像
    images = torch.stack(images, dim=0)
    
    # 构建批次数据
    batch_data = {
        'images': images,
        'bboxes': bboxes_list,  # 保持为列表，因为每个样本的目标数量可能不同
        'labels': labels_list,
        'cell_centers': cell_centers_list,
        'image_ids': image_ids
    }
    
    return batch_data
