**角色:** 你是一名资深的AI架构师，精通PyTorch和HuggingFace生态，擅长将复杂的模型迁移和重构任务，规划为清晰、可执行、小步快跑的迭代开发计划。

**目标:** 为深度学习项目 `LORE-TSR` 迁移至现代化训练框架 `train-anything` 的任务，制定一份详细的需求规划和迭代计划。这份计划将是后续开发工作的唯一依据，必须细致、严谨，确保每个迭代步骤都是一个可独立开发和验证的模块。

---

### **背景信息**

#### **1. 整体迁移目标**

将表格结构识别项目 `LORE-TSR` 从其独立的、基于自定义脚本的架构，完整迁移到 `train-anything` 框架中。迁移的核心是适配 `train-anything` 的现代化训练流程（基于 `accelerate`）、配置管理（基于 `OmegaConf`）和数据加载机制，同时保持 `LORE-TSR` 核心模型与损失函数的初始定义和交互逻辑不变，以确保算法效果的可复现性。

#### **2. 源项目: `LORE-TSR` 架构分析**

- **入口**: `src/main.py`，一个庞大的主函数，负责所有初始化和流程控制。
- **配置**: 通过 `src/lib/opts.py` 使用 `argparse` 解析命令行参数，配置项硬编码在代码中。
- **训练逻辑**: 自定义的 `BaseTrainer` 类 (`src/lib/trains/base_trainer.py`)，使用 `DataParallel` 实现多GPU训练。
- **模型结构**: 分离式设计，包含两个核心 `nn.Module`：
    1.  **`model`**: 负责视觉特征提取的检测器 (如 DLA, ResNet-DCN)，通过 `create_model` 工厂创建。
    2.  **`Processor`**: 负责逻辑结构恢复的Transformer模块 (`src/lib/models/classifier.py`)。
- **损失函数**: `CtdetLoss` (`src/lib/trains/ctdet.py`)，内部包含了热图、回归、结构等多个子损失的复杂计算逻辑。
- **数据集**: 通过 `get_dataset` 工厂 (`src/lib/datasets/dataset_factory.py`) 动态创建数据集类，数据格式与 `train-anything` 不兼容。
- **关键依赖**: 存在需要手动编译的外部依赖：`DCNv2` 和 `NMS` (Cython)。

#### **3. 目标框架: `train-anything` (参考 `cycle-centernet` 实现)**

- **入口**: 独立的训练脚本，如 `training_loops/table_structure_recognition/train_cycle_centernet.py`。
- **配置**: 使用 `OmegaConf` 解析层级化的 `YAML` 配置文件，如 `configs/table_structure_recognition/cycle_centernet/cycle_centernet_config.yaml`。
- **训练逻辑**: 基于 HuggingFace `accelerate` 库，轻松支持分布式训练和混合精度。
- **模型结构**: 单一、集成的模型类 (如 `CycleCenterNetModel`)，通过模型工厂函数创建。
- **损失函数**: 单一、独立的损失类 (如 `CycleCenterNetLoss`)，通过损失工厂函数创建。
- **数据集**: 静态的 `TableDataset` 类 (`my_datasets/table_structure_recognition/table_dataset.py`)，支持标准化的目录结构和JSON标注格式，具备数据质量过滤功能。

---

### **需求与约束**

请严格遵循以下要求，将迁移任务拆分为多个迭代版本。第一个迭代为MVP版，每个迭代版本都必须能够独立运行和验证。

1.  **聚焦需求规划**: 你的任务是定义“做什么”，而不是“怎么做”。请勿包含具体的代码实现方案。
2.  **小颗粒迭代**: 将整个迁移过程分解为最小的可行步骤。例如，“创建配置文件”是一个步骤，“重构模型”是另一个步骤。

#### **具体功能要求:**

1.  **配置系统迁移 (Iteration 1 - MVP)**
    -   **需求**: 创建 `LORE-TSR` 对应的 `YAML` 配置文件。将 `LORE-TSR/src/lib/opts.py` 中的所有配置项（如学习率、模型架构选择、损失权重、数据路径、保存路径、日志路径等）迁移到新的 `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` 文件中。该配置文件需要支持通过命令行覆盖参数（-o, 即overwrite）。
    -   **产出**: 一个完整的 `lore_tsr_config.yaml` 文件和一个用于解析它的 `args.py` 模块。

2.  **数据集适配 (Iteration 2)**
    -   **需求**: 使 `LORE-TSR` 能够加载和处理 `train-anything` 框架下的标准数据集格式。需要扩展 `my_datasets.table_structure_recognition` 模块以兼容 `LORE-TSR` 的特定数据预处理和目标生成逻辑（如 `prepare_targets`）。
    -   **约束**: 不得改变 `train-anything` 的标准JSON数据格式。`LORE-TSR` 独有的数据处理步骤应作为 `TableTransforms` 或 `target_preparation` 的新增部分被集成。
    -   **产出**: 修改后的 `my_datasets/table_structure_recognition` 目录下的相关文件，使其能够为 `LORE-TSR` 模型准备数据。

3.  **模型与损失函数封装 (Iteration 3)**
    -   **需求**: 将 `LORE-TSR` 的核心模型和损失函数封装成符合 `train-anything` 规范的模块。
        -   **模型**: 将 `model` (检测器) 和 `Processor` (Transformer) 合并成一个单一的 `LORETSRModel` 类。该类应在 `networks/lore_tsr/` 目录下定义。提供一个 `create_lore_tsr_model` 工厂函数。
        -   **损失**: 将 `CtdetLoss` 的逻辑封装进一个新的 `LORETSRLoss` 类。该类应在 `networks/lore_tsr/` 目录下定义。提供一个 `create_lore_tsr_loss` 工厂函数。
    -   **约束**: 封装过程不能改变模型各子模块（DLA, Transformer等）的内部网络定义和计算图，也不能改变损失函数的具体数学计算方式。`LORETSRModel` 必须能够根据 `YAML` 配置文件的参数，灵活选择和加载不同的子模块（如 `dla_34`, `resfpn_18` 等）。
    -   **产出**: `networks/lore_tsr/` 目录，包含模型和损失的定义及工厂函数。

4.  **训练脚本与流程重构 (Iteration 4)**
    -   **需求**: 创建新的训练入口脚本 `training_loops/table_structure_recognition/train_lore_tsr.py`。该脚本需要：
        1.  使用 `accelerate` 框架搭建训练主循环。
        2.  调用前面迭代中创建的配置、数据集、模型和损失函数模块。
        3.  实现完整的训练、验证、模型保存和日志记录流程。
    -   **约束**: 训练流程必须完全替换掉 `LORE-TSR` 原有的 `BaseTrainer` 和 `main.py` 逻辑，全面拥抱 `accelerate`。
    -   **产出**: 一个可独立运行的 `train_lore_tsr.py` 脚本。

5.  **文档与依赖说明 (Final Iteration)**
    -   **需求**: 撰写一份 `README.md` 文档，放在 `training_loops/table_structure_recognition/lore_tsr/` 目录下。文档需说明：
        1.  如何配置 `lore_tsr_config.yaml` 文件。
        2.  如何运行 `train_lore_tsr.py` 脚本。
        3.  **明确指出**: `DCNv2` 和 `NMS` 模块需要用户根据环境（PyTorch/CUDA版本）手动编译，并简要说明这是项目运行的必要前置条件。
    -   **产出**: 一份清晰的 `README.md` 文档。

---

### **参考资料**
1. LORE-TSR项目的调用链文档：@readme_LORE_callchain.md
2. cycle-centernet项目的调用链文档：@cycle_centernet_b_project_analysis.md

请根据以上信息，输出一份结构清晰、迭代步骤明确的开发计划。
将结果输出到 @this_vibecoding/docs/2-migration_lore 目录下的readme_migration_lore_prdplan.md中
