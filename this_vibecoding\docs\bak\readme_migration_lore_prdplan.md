# LORE-TSR 向 `train-anything` 框架迁移需求与迭代计划

## 1. 项目背景与目标

**项目名称:** LORE-TSR 迁移与现代化重构

**核心目标:** 将表格结构识别（TSR）项目 `LORE-TSR` 从其原生的、基于自定义脚本的架构，完整、平滑地迁移至现代化、高可扩展性的 `train-anything` 训练框架。本次迁移旨在利用 `train-anything` 的先进特性（如 `Accelerate` 分布式训练、`OmegaConf` 配置管理、标准化的数据处理流程），优化开发效率和模型训练性能，同时确保 `LORE-TSR` 核心算法的逻辑和精度不受影响。

**迁移原则:**

- **小步快跑，迭代验证:** 将复杂的迁移任务分解为一系列独立的、可测试、可交付的迭代，确保每个阶段都有明确的产出和验证标准。
- **逻辑平移，保证复现性:** 迁移初期，严格保持 `LORE-TSR` 核心模型、损失函数和关键预处理的初始定义和内部逻辑不变，以验证迁移后算法效果的可复现性。
- **拥抱框架，而非改造:** 充分利用 `train-anything` 的设计哲学和工具链，将 `LORE-TSR` 的功能适配到框架中，而不是反向修改框架以适应旧代码。

## 2. 迭代开发计划

### **Iteration 1: 配置系统迁移 (MVP)**

- **迭代目标:** 建立 `LORE-TSR` 在 `train-anything` 框架下的配置基础。将原有基于 `argparse` 的硬编码参数，迁移至 `OmegaConf` 管理的层级化 `YAML` 配置文件，实现配置与代码的解耦。

- **核心需求:**
    1.  **创建配置文件:** 在 `configs/table_structure_recognition/lore_tsr/` 目录下，新建 `lore_tsr_config.yaml` 文件。
    2.  **参数迁移:** 将 `LORE-TSR/src/lib/opts.py` 中定义的所有命令行参数（如 `arch`, `lr`, `batch_size`, `data_dir`, `save_dir`, `loss_weights` 等）完整迁移到 `lore_tsr_config.yaml` 中，并组织成合理的层级结构（如 `model`, `optimizer`, `dataset`, `trainer`）。
    3.  **配置解析:** 创建一个独立的 `args.py` 模块，用于加载 `lore_tsr_config.yaml` 并提供通过命令行覆盖参数的功能（例如 `-o model.arch=dla_34`）。
    4. **参数说明:** 在 `lore_tsr_config.yaml` 中添加参数说明，说明每个参数的含义、默认值和可选值。

- **产出物:**
    1.  `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml`
    2.  用于解析配置的 `args.py` 模块。

- **验收标准:**
    1.  `lore_tsr_config.yaml` 包含所有必要的配置项。
    2.  `args.py` 能够成功加载配置文件，并正确解析为一个可访问的配置对象。
    3.  通过命令行 `-o` 参数可以成功覆盖 `YAML` 文件中的任意配置项。

---

### **Iteration 2: 数据集适配**

- **迭代目标:** 使 `LORE-TSR` 能够消费 `train-anything` 框架下的标准数据集格式，将 `LORE-TSR` 特有的数据预处理和目标（Target）生成逻辑，无缝集成到 `train-anything` 的数据加载流程中。

- **核心需求:**
    1.  **兼容 `TableDataset`:** 扩展 `my_datasets.table_structure_recognition` 模块，使其能够为 `LORE-TSR` 模型准备数据。
    2.  **集成预处理:** 将 `LORE-TSR` 特有的数据增强（如仿射变换）和目标生成逻辑（如高斯热图、尺寸回归、结构关系矩阵的生成），封装为 `TableTransforms` 的一部分或独立的 `target_preparation` 函数。
    3.  **保持数据格式不变:** 整个适配过程不得修改 `train-anything` 的标准JSON标注格式。所有适配工作必须在数据加载和预处理管道中完成。

- **产出物:**
    1.  修改或新增的文件位于 `my_datasets/table_structure_recognition/` 目录，包含为 `LORE-TSR` 定制的数据转换和目标生成逻辑。

- **验收标准:**
    1.  能够使用 `train-anything` 的 `TableDataset` 加载器读取标准格式数据。
    2.  数据加载器（DataLoader）能够成功生成一个批次（batch）的数据，其格式（图像张量和目标字典）与 `LORE-TSR` 模型训练时所需的输入完全一致。

---

### **Iteration 3: 模型与损失函数封装**

- **迭代目标:** 将 `LORE-TSR` 的核心模型（检测器 + 结构恢复器）和复杂的损失计算逻辑，封装成符合 `train-anything` 框架规范的、可被工厂函数实例化的独立模块。

- **核心需求:**
    1.  **模型封装 (`LORETSRModel`):**
        -   在 `networks/lore_tsr/` 目录下创建 `model.py`。
        -   定义 `LORETSRModel` 类，该类继承自 `torch.nn.Module`，内部集成 `LORE-TSR` 的视觉主干网络（`model`，如 DLA/ResNet-DCN）和逻辑结构恢复模块（`Processor`）。
        -   `forward` 方法需清晰地定义数据从输入到最终输出（热图、回归值、结构 logits 等）的全流程。
        -   提供一个 `create_lore_tsr_model` 工厂函数，能够根据 `YAML` 配置动态选择并实例化不同的模型变体（如 `dla_34`, `resfpn_18`）。
    2.  **损失函数封装 (`LORETSRLoss`):**
        -   在 `networks/lore_tsr/` 目录下创建 `loss.py`。
        -   定义 `LORETSRLoss` 类，将 `CtdetLoss` 的完整逻辑（包含多个子损失的计算和加权求和）封装其中。
        -   提供一个 `create_lore_tsr_loss` 工厂函数，用于实例化损失对象。

- **约束:**
    -   封装过程不得改变各子模块（如 DLA、Transformer）的内部网络结构和计算逻辑。
    -   损失函数的数学计算方式必须与原始实现完全一致。

- **产出物:**
    1.  `networks/lore_tsr/` 目录，包含 `model.py`, `loss.py`, 和 `__init__.py`。

- **验收标准:**
    1.  `create_lore_tsr_model` 和 `create_lore_tsr_loss` 函数可以根据配置文件成功创建模型和损失对象。
    2.  给定一个来自 Iteration 2 的模拟数据批次，`LORETSRModel` 能够顺利完成前向传播并返回符合预期的输出张量。
    3.  `LORETSRLoss` 能够根据模型输出和数据标签计算出总损失值。

---

### **Iteration 4: 训练脚本与流程重构**

- **迭代目标:** 废弃 `LORE-TSR` 原有的 `main.py` 和 `BaseTrainer`，创建全新的、基于 `Accelerate` 的训练入口脚本，将所有模块（配置、数据、模型、损失）串联起来，形成一个完整的、可运行的训练流程。

- **核心需求:**
    1.  **创建训练脚本:** 在 `training_loops/table_structure_recognition/` 目录下创建 `train_lore_tsr.py`。
    2.  **集成 `Accelerate`:** 使用 `accelerate` 库初始化训练环境，处理设备分配（CPU/GPU/多GPU）、模型和优化器的准备工作。
    3.  **构建主循环:** 实现标准的训练主循环（`train_one_epoch`）和验证循环（`validate`）。
    4.  **调用核心模块:** 在脚本中调用前序迭代产出的配置解析、数据加载、模型创建和损失函数创建逻辑。
    5.  **实现标准流程:** 集成优化器步骤、学习率调度、梯度裁剪（如果需要）、模型保存（checkpointing）、以及使用 `TensorBoard` 或 `WandB` 进行日志记录。

- **约束:**
    -   训练流程必须完全基于 `Accelerate`，不得保留任何 `DataParallel` 或手动的设备管理代码。

- **产出物:**
    1.  可独立运行的 `training_loops/table_structure_recognition/train_lore_tsr.py` 脚本。

- **验收标准:**
    1.  `python training_loops/table_structure_recognition/train_lore_tsr.py` 命令可以启动训练。
    2.  训练能够在一个或多个GPU上正常运行，损失值能够下降。
    3.  训练过程中的日志（loss, metrics）被正确记录，模型检查点被正确保存。

---

### **Final Iteration: 文档与依赖说明**

- **迭代目标:** 为完成迁移的 `LORE-TSR` 项目提供清晰、完备的使用文档，特别强调其特殊的编译依赖，确保其他开发者能够顺利复现和使用。

- **核心需求:**
    1.  **创建 `README.md`:** 在 `training_loops/table_structure_recognition/lore_tsr/` 目录下创建一个 `README.md` 文件。
    2.  **内容撰写:** 文档需至少包含以下内容：
        -   **功能简介:** 简要说明该模块的功能。
        -   **配置说明:** 详细解释 `lore_tsr_config.yaml` 中关键参数的含义和配置方法。
        -   **运行指南:** 提供单GPU和多GPU（使用 `accelerate launch`）的训练启动命令示例。
        -   **依赖警告:** 在文档的显著位置，明确指出本项目依赖 `DCNv2` 和 `NMS` 两个需要手动编译的外部模块。简要说明这是 `LORE-TSR` 算法的固有依赖，并提醒用户需要根据其具体的 PyTorch 和 CUDA 版本进行编译。

- **产出物:**
    1.  `training_loops/table_structure_recognition/lore_tsr/README.md`

- **验收标准:**
    1.  文档内容清晰、准确，无歧义。
    2.  一个不熟悉该项目的新用户，能够根据文档成功配置环境、准备数据并启动训练。
