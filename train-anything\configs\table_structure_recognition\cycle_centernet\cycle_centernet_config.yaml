# Cycle-CenterNet 表格结构识别训练配置文件
# Time: 2025-01-04
# Author: <EMAIL>
# Description: 基于OmegaConf的层级配置文件，支持命令行参数覆盖

# ============================================================================
# 基础配置
# ============================================================================
basic:
  # 调试模式
  debug: false
  # 随机种子
  seed: 42
  # 输出目录
  output_dir: /aipdf-mlp/xelawk/training_outputs/tsr_training/cycle-centernet/debug
  # 只执行可视化功能，不进行训练和验证
  only_vis_log: false

# ============================================================================
# 数据配置
# ============================================================================
data:
  # 数据路径配置 - 支持单个路径或多个路径列表
  paths:
    # 训练数据目录 - 可以是单个路径字符串或路径列表
    train_data_dir:
      - /aipdf-mlp/shared/tsr_training/wired_table/release/TabRecSet_chinese/train
      - /aipdf-mlp/shared/tsr_training/wired_table/release/TabRecSet_english/train
      - /aipdf-mlp/shared/tsr_training/wired_table/release/WTW/train
      - /aipdf-mlp/shared/tsr_training/wired_table/release/TALOCRTable/train
      # - /path/to/additional/train/data  # 可添加更多训练数据目录

    # 验证数据目录 - 可以是单个路径字符串或路径列表
    val_data_dir:
      - /aipdf-mlp/shared/tsr_training/wired_table/release/TabRecSet_chinese/val
      - /aipdf-mlp/shared/tsr_training/wired_table/release/TabRecSet_english/val
      - /aipdf-mlp/shared/tsr_training/wired_table/release/WTW/val
      - /aipdf-mlp/shared/tsr_training/wired_table/release/TALOCRTable/val
      # - /path/to/additional/val/data    # 可添加更多验证数据目录

  # 数据处理配置
  processing:
    # 最大样本数量，用于调试，null表示使用全部数据
    max_samples: 1000
    # 图像尺寸 [height, width]
    image_size: [1024, 1024]
    # 图像归一化参数 (ImageNet标准)
    normalize:
      mean: [0.485, 0.456, 0.406]
      std: [0.229, 0.224, 0.225]
      to_rgb: true

  # 数据加载配置
  loader:
    # 数据加载器工作进程数
    num_workers: 16
    # 是否使用pin_memory
    pin_memory: true

# ============================================================================
# 模型配置
# ============================================================================
model:
  # 骨干网络配置
  backbone:
    # DLA骨干网络深度
    depth: 34
    # 预训练权重路径（null表示不使用预训练权重）
    pretrained: "http://dl.yf.io/dla/models/imagenet/dla34%2Btricks-24a49e58.pth"

  # 检测头配置
  head:
    # 检测头特征通道数
    feat_channels: 64

  # 任务配置
  task:
    # 类别数量
    num_classes: 1

# ============================================================================
# 训练配置
# ============================================================================
training:
  # 基础训练参数
  epochs: 200
  batch_size: 16

  # 优化器配置
  optimizer:
    # 优化器类型 (SGD/Adam/AdamW)
    type: "Adam"
    # 学习率
    learning_rate: 0.00125
    # 权重衰减
    weight_decay: 0.0001
    # SGD动量
    momentum: 0.9

    # Adam/AdamW特定参数
    adam:
      use_adam: false
      use_8bit_adamw: false
      beta1: 0.9
      beta2: 0.999
      weight_decay: 0.01
      epsilon: 1e-08

  # 学习率调度器配置
  scheduler:
    # 调度器类型 (constant/constant_with_warmup/cosine/cosine_with_restarts/linear)
    type: "constant_with_warmup"
    # 余弦退火参数
    cosine:
      num_cycles: 1
    # 预热配置
    warmup:
      steps: 500
    # 其他参数
    power: 1.0

  # 梯度配置
  gradient:
    # 是否使用梯度裁剪
    clip_norm: false
    # 梯度裁剪阈值
    clip_value: 1.0
    # 梯度累积步数
    accumulation_steps: 1

# ============================================================================
# EMA配置
# ============================================================================
ema:
  # 是否启用EMA
  enabled: false
  # EMA衰减率
  decay: 0.999
  # EMA开始步数
  start_step: 0
  # EMA更新周期
  update_period: 1

# ============================================================================
# 损失函数配置
# ============================================================================
loss:
  # 各损失函数权重
  weights:
    heatmap: 1.0          # 中心点热图损失权重
    offset: 1.0           # 偏移损失权重
    center2vertex: 1.0    # 中心到顶点损失权重
    vertex2center: 0.5    # 顶点到中心损失权重

# ============================================================================
# 检查点和验证配置
# ============================================================================
checkpoint:
  # 保存配置
  save:
    # 保存检查点的步数间隔
    steps: 2000
    # 每N个epoch保存一次模型
    every_n_epoch: 1
    # 保留的检查点数量
    keep_num: 100

  # 恢复配置
  resume:
    # 从检查点恢复训练的路径
    from_checkpoint: null

  # 验证配置
  validation:
    # 验证时使用的批次数量
    num_batches: 10

# ============================================================================
# 分布式训练配置
# ============================================================================
distributed:
  # 混合精度训练 (no/fp16/bf16)
  mixed_precision: "no"
  # 日志记录工具
  report_to: "tensorboard"
  # 跟踪器项目名称
  tracker_project_name: "cycle-centernet-training"

# ============================================================================
# 可视化配置
# ============================================================================
visualization:
  # 是否启用可视化功能
  enabled: true
  # 可视化样本图片路径（用于纯图片可视化，不依赖验证集标注）
  sample_images_dir: "assets/vis4tsr"
  # 每次可视化的样本数量
  max_samples: 10
  # 可视化结果保存路径（null时默认为basic.output_dir/visualization_results）
  output_dir: null
  # 可视化频率：每N次验证进行一次可视化（1表示每次验证都可视化）
  frequency: 1

  # 可视化样式配置
  style:
    bbox_color: [0, 255, 0]        # 边界框颜色 (绿色)
    keypoint_color: [255, 0, 0]    # 关键点颜色 (红色)
    center_color: [255, 0, 0]      # 中心点颜色 (红色)
    transparency: 0.8              # 透明度
    line_thickness: 2              # 线条粗细
    point_radius: 4                # 点的半径

  # 热图可视化配置
  heatmap:
    colormap: "jet"                # 颜色映射
    normalize: true                # 是否归一化
    threshold: 0.1                 # 显示阈值

# ============================================================================
# 调试与可视化配置
# ============================================================================
debug_visualization:
  # 是否启用干运行模式（只加载数据并可视化，不训练模型）
  dry_run: false
  # 干运行模式下可视化的批次数量
  dry_run_batches: 5
  # 干运行模式下可视化结果保存路径（null时默认为basic.output_dir/dry_run_results）
  dry_run_output_dir: null

  # 干运行可视化样式配置
  style:
    center_point_color: [255, 0, 0]      # 中心点颜色 (红色)
    bbox_color: [0, 255, 0]              # 边界框颜色 (绿色)
    offset_color: [0, 0, 255]            # 偏移向量颜色 (蓝色)
    center2vertex_color: [255, 0, 0]     # 中心到顶点颜色 (红色)
    vertex2center_color: [255, 255, 0]   # 顶点到中心颜色 (黄色)
    line_thickness: 2                    # 线条粗细
    point_radius: 4                      # 点的半径
    vector_scale: 10.0                   # 向量显示放大倍数