
## 动态迁移蓝图 (V1.0)

### 1. 文件迁移映射表

此表映射了 `LORE-TSR` 的**核心文件**（未包括所有，但迁移的目标是所有必要文件，随着迁移计划扫盘并更新）到 `train-anything` 框架中的目标路径，并定义了迁移策略和当前状态。

| 源文件 (LORE-TSR) | 目标文件 (train-anything) | 迁移策略 (说明) | 状态 |
| :--- | :--- | :--- | :--- |
| **入口与配置** | | | |
| `src/main.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | **重构**: 以 `cycle-centernet-ms` 为模板，适配 `accelerate` 训练循环。 | `未开始` |
| `src/lib/opts.py` | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | **重构**: 转换为 `OmegaConf` 的 YAML 格式，并集成到 `train-anything` 配置体系。 | `未开始` |
| **数据集** | | | |
| `src/lib/datasets/dataset_factory.py` | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | **重构**: 创建新的 `lore_tsr_dataset.py`，遵循 `train-anything` 的数据集标准。 | `未开始` |
| `src/lib/datasets/table_dataset/table.py` | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | **重构**: 核心数据加载和预处理逻辑将被整合进新的 `lore_tsr_dataset.py` 中。 | `未开始` |
| **训练器/检测器** | | | |
| `src/lib/detectors/base_detector.py` | (由框架取代) | **废弃**: `train-anything` 的 `training_loop` 提供了更高级的抽象。 | `未开始` |
| `src/lib/detectors/ctdet.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | **重构**: 核心的 `run_epoch` 逻辑将被迁移并适配到新的训练循环中。 | `未开始` |
| **核心模型与算法** | | | |
| `src/lib/models/model.py` | `networks/lore_tsr/model_factory.py` | **复制与适配**: `create_model` 逻辑将被复制和适配。`load_model` 由框架处理。 | `未开始` |
| `src/lib/models/networks/dla.py` | `networks/lore_tsr/dla.py` | **复制**: 核心骨干网络，直接复制并调整 `import`。 | `未开始` |
| `src/lib/models/losses.py` | `networks/lore_tsr/losses.py` | **复制**: 核心损失函数，直接复制并调整 `import`。 | `未开始` |
| `src/lib/utils/post_process.py` | `networks/lore_tsr/post_process.py` | **复制**: 核心后处理逻辑，直接复制并调整 `import`。 | `未开始` |
| **编译依赖** | | | |
| `src/lib/external/DCNv2/` | `external/lore_tsr/DCNv2/` | **复制与隔离**: 复制源代码，待后续手动编译。 | `未开始` |
| `src/lib/external/nms.pyx` | `external/lore_tsr/nms.pyx` | **复制与隔离**: 复制源代码，待后续手动编译。 | `未开始` |

### 2. 逻辑依赖与迁移路径图 (Mermaid)

此图可视化了核心模块的迁移路径和它们之间的关键依赖关系。

```mermaid
graph TD
    %% ------------------- Source: LORE-TSR -------------------
    subgraph "Source: LORE-TSR"
        direction LR
        S_main("src/main.py")
        S_opts("src/lib/opts.py")
        S_detector("src/lib/detectors/ctdet.py")
        S_dataset("src/lib/datasets/table_dataset/table.py")
        S_model("src/lib/models/model.py")
        S_dla("src/lib/models/networks/dla.py")
        S_dcn("src/lib/external/DCNv2/")
    end

    %% ------------------- Target: train-anything -------------------
    subgraph "Target: train-anything"
        direction LR
        T_train_loop("training_loops/.../train_lore_tsr.py")
        T_config("configs/.../lore_tsr_config.yaml")
        T_dataset("my_datasets/.../lore_tsr_dataset.py")
        T_model_factory("networks/lore_tsr/model_factory.py")
        T_dla("networks/lore_tsr/dla.py")
        T_dcn("external/lore_tsr/DCNv2/")
        T_framework("[Framework Trainer]")
    end

    %% ------------------- Mappings & Dependencies -------------------
    S_main -- "Refactor" --> T_train_loop
    S_opts -- "Refactor" --> T_config
    S_detector -- "Refactor" --> T_train_loop
    S_dataset -- "Refactor" --> T_dataset
    S_model -- "Copy & Adapt" --> T_model_factory
    S_dla -- "Copy" --> T_dla
    S_dcn -- "Copy & Isolate" --> T_dcn

    T_train_loop -.-> T_config
    T_train_loop -.-> T_dataset
    T_train_loop -.-> T_model_factory
    T_model_factory -.-> T_dla
    T_dla -.-> T_dcn
```

### 3. 目标目录结构树

这是 `LORE-TSR` 迁移完成后，在 `train-anything` 项目中期望的最终目录结构（暂未包括所有LORE-TSR项目文件，需要随着迁移计划扫盘并更新）。

```text
train-anything/
├── configs/
│   └── table_structure_recognition/
│       └── lore_tsr/
│           └── lore_tsr_config.yaml  [placeholder]
├── external/
│   └── lore_tsr/                   [placeholder]
│       ├── DCNv2/                  [placeholder]
│       └── nms.pyx                 [placeholder]
├── my_datasets/
│   └── table_structure_recognition/
│       └── lore_tsr_dataset.py     [placeholder]
├── networks/
│   └── lore_tsr/
│       ├── dla.py                  [placeholder]
│       ├── losses.py               [placeholder]
│       ├── model_factory.py        [placeholder]
│       └── post_process.py         [placeholder]
└── training_loops/
    └── table_structure_recognition/
        └── train_lore_tsr.py       [placeholder]
```

---

## 编码计划：Step 1

**步骤 1.1: 创建项目骨架目录**

**目标:** 为 `LORE-TSR` 迁移建立基础的、空的目录结构。这是所有后续代码和配置文件的容器，确保迁移从一个清晰、规范的结构开始。

**影响文件:**
*   (无文件创建，仅创建目录)

**具体操作:**
1.  在 `train-anything` 项目的根目录下，根据“目标目录结构树”创建所有必需的空目录。

**如何验证 (Verification):**
*   执行以下 `shell` 命令，并检查其输出是否与“目标目录结构树”中定义的结构完全一致（忽略 `[placeholder]` 文件）。

```shell
# 在 train-anything 项目根目录下运行
ls -R configs/table_structure_recognition/lore_tsr \
  external/lore_tsr \
  my_datasets/table_structure_recognition \
  networks/lore_tsr \
  training_loops/table_structure_recognition
```

*   **预期输出:** 命令会列出所有新创建的目录。如果目录不存在，命令会报错，表示步骤失败。如果成功，将不会有错误信息，并且你可以通过 `tree` 命令（如果可用）或手动检查来确认结构。
