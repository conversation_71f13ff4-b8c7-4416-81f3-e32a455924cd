
## 动态迁移蓝图 (V1.1)

### 1. 文件迁移映射表

目录骨架已创建，但文件迁移尚未开始。因此，所有文件的状态仍为 `未开始`。

| 源文件 (LORE-TSR) | 目标文件 (train-anything) | 迁移策略 (说明) | 状态 |
| :--- | :--- | :--- | :--- |
| **入口与配置** | | | |
| `src/main.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | **重构**: 以 `cycle-centernet-ms` 为模板，适配 `accelerate` 训练循环。 | `未开始` |
| `src/lib/opts.py` | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | **重构**: 转换为 `OmegaConf` 的 YAML 格式，并集成到 `train-anything` 配置体系。 | `未开始` |
| **数据集** | | | |
| `src/lib/datasets/dataset_factory.py` | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | **重构**: 创建新的 `lore_tsr_dataset.py`，遵循 `train-anything` 的数据集标准。 | `未开始` |
| `src/lib/datasets/table_dataset/table.py` | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | **重构**: 核心数据加载和预处理逻辑将被整合进新的 `lore_tsr_dataset.py` 中。 | `未开始` |
| **训练器/检测器** | | | |
| `src/lib/detectors/base_detector.py` | (由框架取代) | **废弃**: `train-anything` 的 `training_loop` 提供了更高级的抽象。 | `未开始` |
| `src/lib/detectors/ctdet.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | **重构**: 核心的 `run_epoch` 逻辑将被迁移并适配到新的训练循环中。 | `未开始` |
| **核心模型与算法** | | | |
| `src/lib/models/model.py` | `networks/lore_tsr/model_factory.py` | **复制与适配**: `create_model` 逻辑将被复制和适配。`load_model` 由框架处理。 | `未开始` |
| `src/lib/models/networks/dla.py` | `networks/lore_tsr/dla.py` | **复制**: 核心骨干网络，直接复制并调整 `import`。 | `未开始` |
| `src/lib/models/losses.py` | `networks/lore_tsr/losses.py` | **复制**: 核心损失函数，直接复制并调整 `import`。 | `未开始` |
| `src/lib/utils/post_process.py` | `networks/lore_tsr/post_process.py` | **复制**: 核心后处理逻辑，直接复制并调整 `import`。 | `未开始` |
| **编译依赖** | | | |
| `src/lib/external/DCNv2/` | `external/lore_tsr/DCNv2/` | **复制与隔离**: 复制源代码，待后续手动编译。 | `进行中` |
| `src/lib/external/nms.pyx` | `external/lore_tsr/nms.pyx` | **复制与隔离**: 复制源代码，待后续手动编译。 | `进行中` |

### 2. 逻辑依赖与迁移路径图 (Mermaid)

此步骤将填充 `external/` 目录，是后续模型依赖的基础。

```mermaid
graph TD
    %% ------------------- Source: LORE-TSR -------------------
    subgraph "Source: LORE-TSR"
        direction LR
        S_main("src/main.py")
        S_opts("src/lib/opts.py")
        S_detector("src/lib/detectors/ctdet.py")
        S_dataset("src/lib/datasets/table_dataset/table.py")
        S_model("src/lib/models/model.py")
        S_dla("src/lib/models/networks/dla.py")
        S_dcn("src/lib/external/DCNv2/")
        S_nms("src/lib/external/nms.pyx")
    end

    %% ------------------- Target: train-anything -------------------
    subgraph "Target: train-anything"
        direction LR
        T_train_loop("training_loops/.../train_lore_tsr.py")
        T_config("configs/.../lore_tsr_config.yaml")
        T_dataset("my_datasets/.../lore_tsr_dataset.py")
        T_model_factory("networks/lore_tsr/model_factory.py")
        T_dla("networks/lore_tsr/dla.py")
        T_dcn("external/lore_tsr/DCNv2/")
        T_nms("external/lore_tsr/nms.pyx")
    end

    %% ------------------- Mappings & Dependencies -------------------
    S_dcn -- "Copy & Isolate" --> T_dcn
    S_nms -- "Copy & Isolate" --> T_nms
    S_dla -- "Copy" --> T_dla
    T_dla -.-> T_dcn

    S_model -- "Copy & Adapt" --> T_model_factory
    T_model_factory -.-> T_dla

    S_main -- "Refactor" --> T_train_loop
    S_detector -- "Refactor" --> T_train_loop
    T_train_loop -.-> T_model_factory
```

### 3. 目标目录结构树

根据步骤1的执行报告，目录已成功创建。我们现在准备向其中填充文件。

```text
train-anything/
├── configs/
│   └── table_structure_recognition/
│       └── lore_tsr/
│           └── lore_tsr_config.yaml  [placeholder]
├── external/
│   └── lore_tsr/
│       ├── DCNv2/                  [placeholder]
│       └── nms.pyx                 [placeholder]
├── my_datasets/
│   └── table_structure_recognition/
│       └── lore_tsr_dataset.py     [placeholder]
├── networks/
│   └── lore_tsr/
│       ├── dla.py                  [placeholder]
│       ├── losses.py               [placeholder]
│       ├── model_factory.py        [placeholder]
│       └── post_process.py         [placeholder]
└── training_loops/
    └── table_structure_recognition/
        └── train_lore_tsr.py       [placeholder]
```

---

## 编码计划：Step 2

**步骤 2.1: 复制并隔离编译依赖项**

**目标:** 遵循迁移黄金法则第三条，将需要手动编译的依赖项 `DCNv2` 和 `NMS` 从源项目 `LORE-TSR` 原封不动地复制到 `train-anything` 的 `external/lore_tsr/` 目录下。此步骤是后续模型能够成功构建和运行的关键前置条件。

**影响文件:**
*   `train-anything/external/lore_tsr/DCNv2/` (创建并填充)
*   `train-anything/external/lore_tsr/nms.pyx` (创建并填充)

**具体操作:**
1.  **复制 `DCNv2`:**
    *   源路径: `LORE-TSR/src/lib/external/DCNv2/`
    *   目标路径: `train-anything/external/lore_tsr/DCNv2/`
    *   执行 `cp -r LORE-TSR/src/lib/external/DCNv2/ train-anything/external/lore_tsr/DCNv2/` 命令。
2.  **复制 `NMS`:**
    *   源路径: `LORE-TSR/src/lib/external/nms.pyx`
    *   目标路径: `train-anything/external/lore_tsr/nms.pyx`
    *   执行 `cp LORE-TSR/src/lib/external/nms.pyx train-anything/external/lore_tsr/nms.pyx` 命令。

**如何验证 (Verification):**
*   执行以下 `shell` 命令，检查目标目录和文件是否已成功创建。

```shell
# 在 train-anything 项目的父目录下运行
ls -l train-anything/external/lore_tsr/
```

*   **预期输出:**
    *   命令的输出中应包含 `DCNv2` 目录和 `nms.pyx` 文件。
    *   进一步执行 `ls -l train-anything/external/lore_tsr/DCNv2/` 应能看到 `DCNv2` 的内部源文件（如 `src`, `setup.py` 等），确认是完整复制而非空目录。
    *   如果任一文件或目录缺失，则表示步骤失败。

---
