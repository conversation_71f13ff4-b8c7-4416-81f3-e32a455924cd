## 动态迁移蓝图 (V2.0)

**最后更新时间:** 2025-07-16T22:10:36+08:00

### 1. 文件迁移映射表

| 源文件 (LORE-TSR) | 目标文件 (train-anything) | 迁移策略 (说明) | 状态 |
| :--- | :--- | :--- | :--- |
| `src/lib/opts.py` | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | **重构与适配**: 智能地将 `argparse` 参数转换为结构化的 `OmegaConf` YAML。 | `进行中` |
| ... | ... | ... | ... |

### 2. 逻辑依赖与迁移路径图 (Mermaid)

```mermaid
graph TD
    subgraph "Source & References"
        S_opts("LORE-TSR/src/lib/opts.py")
        S_prd("PRD: Iteration 1 Design")
        T_template("train-anything/configs/.../cycle_centernet_ms_config.yaml")
    end

    subgraph "Target: train-anything"
        T_config("configs/.../lore_tsr_config.yaml")
    end

    %% 迁移路径
    S_opts -- "Provides Hyperparameter Values" --> T_config
    T_template -- "Provides Structure & Style" --> T_config
    S_prd -- "Defines 'data' Section Rules" --> T_config
```

### 3. 目标目录结构树

```
train-anything/
├── configs/
│   └── table_structure_recognition/
│       └── lore_tsr/
│           └── lore_tsr_config.yaml  [进行中]
...
```

---

## 编码计划 (Step 5 - 修订版)

### 步骤 5.1: 智能重构配置系统 (`opts.py` -> `lore_tsr_config.yaml`)

**目标:** 严格遵循需求文档（PRD）和目标框架的最佳实践，将 `opts.py` 中的核心参数智能地、结构化地迁移到新的 `lore_tsr_config.yaml` 文件中，并果断舍弃不再需要的旧参数。

**核心参考:**
1.  **源参数值:** `LORE-TSR/src/lib/opts.py`
2.  **目标结构模板:** `train-anything/configs/table_structure_recognition/cycle_centernet/cycle_centernet_ms_config.yaml`
3.  **设计规范:** `this_vibecoding/docs/2-migration_lore/1-readme_migration_lore_prdplan.md` 中 “Iteration 1” 的详细需求。

**影响文件:**
*   `train-anything/configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` (创建并填充)

**编码指令:**

1.  **创建文件并建立骨架:**
    *   创建目标文件。
    *   以 `cycle_centernet_ms_config.yaml` 为模板，在文件中建立顶层结构，至少包含 `basic`, `data`, `model`, `training`, `loss`, `checkpoint` 几个部分。

2.  **迁移 `basic`, `model`, `training`, `loss` 参数:**
    *   **`basic`**: 迁移 `seed`, `debug`。设置一个合理的 `output_dir`。
    *   **`model`**: 迁移 `arch` (改为 `base_name`), `head_conv`, `down_ratio`。
    *   **`training`**: 迁移 `lr` (改为 `optimizer.learning_rate`), `batch_size`, `num_epochs` (改为 `epochs`)。根据 `optim` 参数，设置 `optimizer.type` (如 `adam`) 及相关参数 (`weight_decay`)。
    *   **`loss`**: 迁移所有损失权重，如 `hm_weight`, `wh_weight`, `off_weight`, `st_weight`, `ax_weight`, `cr_weight`，将它们放入 `loss.weights` 子节点下。

3.  **严格按PRD重构 `data` 部分 (关键步骤):**
    *   **`data.paths`**: 创建 `train_data_dir` 和 `val_data_dir` 键，并提供占位符路径，如 `/path/to/your/train/data`。
    *   **`data.loader`**: 迁移 `num_workers`，并添加 `pin_memory: true`。
    *   **`data.processing`**: 
        *   迁移 `input_res` (改为 `image_size`)，`mean`，`std`。
        *   创建 `augmentation` 子节点，这是重中之重。
        *   将 `opts.py` 中的数据增强相关标志 (如 `not_rand_crop`, `aug_lighting`, `color_aug`, `shift`, `scale`, `rotate`) 转换为 `augmentation` 下的布尔值或数值键值对。例如 `not_rand_crop: true` 应变为 `rand_crop: false`。

4.  **明确废弃的参数 (重要):**
    *   在执行过程中，**必须忽略** `opts.py` 中的以下参数，因为它们的功能已被 `train-anything` 框架或 `accelerate` 库接管：
        *   `gpus`, `master_port` (由 `accelerate` 处理)
        *   `task`, `dataset`, `exp_id` (由配置文件路径和框架逻辑定义)
        *   `resume`, `load_model` (由 `checkpoint.resume.from_checkpoint` 统一管理)
        *   `lr_step` (由 `training.scheduler` 统一管理)

**验收标准:**

1.  **文件存在性与格式:** 目标文件已创建且为合法的YAML。
2.  **结构符合性:** 文件的顶级结构与 `cycle_centernet_ms_config.yaml` 保持一致。
3.  **PRD合规性 (高优先级):** `data` 部分的结构完全符合PRD中 “Iteration 1” 的设计，特别是 `data.processing.augmentation` 子节点的正确创建和参数映射。
4.  **参数完整性:** `arch`, `lr`, `batch_size`, `hm_weight` 等核心超参数已正确迁移到其在YAML中的新位置。
5.  **参数废弃:** 确认 `gpus`, `task`, `lr_step` 等被废弃的参数未出现在新的配置文件中。

---
