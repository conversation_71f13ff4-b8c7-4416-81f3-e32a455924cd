# Time: 2025-07-16
# Author: AI Assistant
# FileName: __init__.py

"""
LORE-TSR 工具模块

这个模块包含了从 LORE-TSR 项目迁移的工具函数，主要用于支持 LORE-TSR 模型的
特征处理、图像变换等核心算法操作。

主要组件:
- utils: 特征提取和处理工具函数
- image: 图像变换和预处理工具函数
"""

from .utils import (
    _sigmoid,
    _gather_feat,
    _flatten_and_gather_feat,
    _tranpose_and_gather_feat,
    _h_dist_feat,
    _v_dist_feat,
    _make_pair_feat,
    _get_4ps_feat
)

from .image import (
    transform_preds,
    transform_preds_upper_left,
    flip,
    get_affine_transform,
    get_affine_transform_upper_left,
    affine_transform
)

__all__ = [
    # utils functions
    '_sigmoid',
    '_gather_feat', 
    '_flatten_and_gather_feat',
    '_tranpose_and_gather_feat',
    '_h_dist_feat',
    '_v_dist_feat',
    '_make_pair_feat',
    '_get_4ps_feat',
    # image functions
    'transform_preds',
    'transform_preds_upper_left',
    'flip',
    'get_affine_transform',
    'get_affine_transform_upper_left',
    'affine_transform'
]
