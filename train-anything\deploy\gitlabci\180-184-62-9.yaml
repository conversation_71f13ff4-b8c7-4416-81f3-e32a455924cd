# 火山引擎, RTX4090Dx1, 22C, 80G

aipdf-hsyq_1gpus_workspace_1022:
  rules:
    - if: $CI_COMMIT_TAG =~ /^v.*-aipdf-deploy$/
      when: manual
  script:
    - docker-compose -f deploy/compose/aipdf-hsyq-4090Dx1-allgpus-1022.yaml -p aipdf_hsyq_1gpus_workspace_1022 up --build --remove-orphans -d
  stage: deploy
  tags:
    - HSYQ-RTX4090Dx1-001

aipdf-hsyq_1gpus_workspace_1122:
  rules:
    - if: $CI_COMMIT_TAG =~ /^v.*-aipdf-deploy$/
      when: manual
  script:
    - docker-compose -f deploy/compose/aipdf-hsyq-4090Dx1-allgpus-1122.yaml -p aipdf_hsyq_1gpus_workspace_1122 up --build --remove-orphans -d
  stage: deploy
  tags:
    - HSYQ-RTX4090Dx1-001

aipdf-hsyq_1gpus_workspace_1222:
  rules:
    - if: $CI_COMMIT_TAG =~ /^v.*-aipdf-deploy$/
      when: manual
  script:
    - docker-compose -f deploy/compose/aipdf-hsyq-4090Dx1-allgpus-1222.yaml -p aipdf_hsyq_1gpus_workspace_1222 up --build --remove-orphans -d
  stage: deploy
  tags:
    - HSYQ-RTX4090Dx1-001

aipdf-hsyq_1gpus_workspace_1322:
  rules:
    - if: $CI_COMMIT_TAG =~ /^v.*-aipdf-deploy$/
      when: manual
  script:
    - docker-compose -f deploy/compose/aipdf-hsyq-4090Dx1-allgpus-1322.yaml -p aipdf_hsyq_1gpus_workspace_1322 up --build --remove-orphans -d
  stage: deploy
  tags:
    - HSYQ-RTX4090Dx1-001

aipdf-hsyq_1gpus_workspace_1422:
  rules:
    - if: $CI_COMMIT_TAG =~ /^v.*-aipdf-deploy$/
      when: manual
  script:
    - docker-compose -f deploy/compose/aipdf-hsyq-4090Dx1-allgpus-1422.yaml -p aipdf_hsyq_1gpus_workspace_1422 up --build --remove-orphans -d
  stage: deploy
  tags:
    - HSYQ-RTX4090Dx1-001

aipdf-hsyq_1gpus_workspace_1522:
  rules:
    - if: $CI_COMMIT_TAG =~ /^v.*-aipdf-deploy$/
      when: manual
  script:
    - docker-compose -f deploy/compose/aipdf-hsyq-4090Dx1-allgpus-1522.yaml -p aipdf_hsyq_1gpus_workspace_1522 up --build --remove-orphans -d
  stage: deploy
  tags:
    - HSYQ-RTX4090Dx1-001