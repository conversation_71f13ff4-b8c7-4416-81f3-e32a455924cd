#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025-01-03 12:00
# <AUTHOR> <EMAIL>
# @FileName: cycle_centernet_loss.py

"""
Cycle-CenterNet损失函数实现

基于原Cycle-CenterNet项目中的损失函数实现，
适配train-anything项目的架构要求。

包含：
1. GaussianFocalLoss - 中心点热图损失
2. L1Loss - 偏移和向量回归损失
3. CycleCenterNetLoss - 组合损失函数
"""

import math
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Any, Optional, Tuple


def gaussian_focal_loss(
    pred: torch.Tensor,
    target: torch.Tensor,
    alpha: float = 2.0,
    gamma: float = 4.0,
    eps: float = 1e-12
) -> torch.Tensor:
    """
    Gaussian Focal Loss核心计算函数
    
    基于CornerNet论文中的Focal Loss变体，专门用于处理高斯分布的热图目标。
    
    Args:
        pred: 预测值，形状为 (N, C, H, W)，值域为 [0, 1]
        target: 目标值，形状为 (N, C, H, W)，高斯分布热图
        alpha: 正样本的调制因子，默认为2.0
        gamma: 负样本的调制因子，默认为4.0
        eps: 数值稳定性常数
        
    Returns:
        损失张量，形状与输入相同
    """
    # 正样本权重：目标值为1的位置
    pos_weights = target.eq(1.0)
    
    # 负样本权重：使用 (1 - target)^gamma 进行调制
    neg_weights = (1 - target).pow(gamma)
    
    # 正样本损失：-log(pred) * (1 - pred)^alpha * pos_weights
    pos_loss = -(pred + eps).log() * (1 - pred).pow(alpha) * pos_weights
    
    # 负样本损失：-log(1 - pred) * pred^alpha * neg_weights
    neg_loss = -(1 - pred + eps).log() * pred.pow(alpha) * neg_weights
    
    return pos_loss + neg_loss


def compute_pairing_weight(
    c2v_pred: torch.Tensor,
    c2v_target: torch.Tensor,
    v2c_pred: torch.Tensor,
    v2c_target: torch.Tensor,
    center_coords: torch.Tensor,
    vertex_coords: torch.Tensor
) -> torch.Tensor:
    """
    计算Pairing Loss的动态加权函数

    根据论文公式：ω(P_cv) = 1 - exp(-π * D_cv)
    其中 D_cv 是归一化后的中心-顶点对的偏移误差

    Args:
        c2v_pred: center2vertex预测值
        c2v_target: center2vertex目标值
        v2c_pred: vertex2center预测值
        v2c_target: vertex2center目标值
        center_coords: 中心点坐标
        vertex_coords: 顶点坐标

    Returns:
        动态权重张量
    """
    # 计算预测误差
    c2v_error = torch.abs(c2v_pred - c2v_target)
    v2c_error = torch.abs(v2c_pred - v2c_target)

    # 计算归一化误差 D_cv
    # 使用目标偏移的绝对值作为归一化因子
    c2v_norm = torch.abs(c2v_target) + 1e-8  # 避免除零
    D_cv = torch.clamp((c2v_error + v2c_error) / c2v_norm, max=1.0)

    # 计算动态权重：ω(P_cv) = 1 - exp(-π * D_cv)
    pi = math.pi
    weight = 1.0 - torch.exp(-pi * D_cv)

    return weight


class GaussianFocalLoss(nn.Module):
    """
    Gaussian Focal Loss损失函数
    
    专门用于CenterNet风格的中心点热图预测，能够处理高斯分布的目标热图。
    
    Args:
        alpha: 正样本调制因子
        gamma: 负样本调制因子
        reduction: 损失归约方式 ('none', 'mean', 'sum')
        loss_weight: 损失权重
    """
    
    def __init__(
        self,
        alpha: float = 2.0,
        gamma: float = 4.0,
        reduction: str = 'mean',
        loss_weight: float = 1.0
    ):
        super(GaussianFocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        self.loss_weight = loss_weight
    
    def forward(
        self,
        pred: torch.Tensor,
        target: torch.Tensor,
        weight: Optional[torch.Tensor] = None,
        avg_factor: Optional[int] = None,
        reduction_override: Optional[str] = None
    ) -> torch.Tensor:
        """
        前向传播计算损失
        
        Args:
            pred: 预测热图，形状为 (N, C, H, W)
            target: 目标热图，形状为 (N, C, H, W)
            weight: 样本权重，可选
            avg_factor: 平均因子，用于归一化
            reduction_override: 覆盖默认的归约方式
            
        Returns:
            损失值
        """
        assert reduction_override in (None, 'none', 'mean', 'sum')
        reduction = reduction_override if reduction_override else self.reduction
        
        # 计算基础损失
        loss = gaussian_focal_loss(
            pred=pred,
            target=target,
            alpha=self.alpha,
            gamma=self.gamma
        )
        
        # 应用样本权重
        if weight is not None:
            loss = loss * weight
        
        # 应用归约
        if reduction == 'mean':
            if avg_factor is not None:
                loss = loss.sum() / avg_factor
            else:
                loss = loss.mean()
        elif reduction == 'sum':
            loss = loss.sum()
        # reduction == 'none' 时不做处理
        
        return self.loss_weight * loss


class L1Loss(nn.Module):
    """
    L1损失函数
    
    用于偏移回归和向量回归任务。
    
    Args:
        reduction: 损失归约方式
        loss_weight: 损失权重
    """
    
    def __init__(
        self,
        reduction: str = 'mean',
        loss_weight: float = 1.0
    ):
        super(L1Loss, self).__init__()
        self.reduction = reduction
        self.loss_weight = loss_weight
    
    def forward(
        self,
        pred: torch.Tensor,
        target: torch.Tensor,
        weight: Optional[torch.Tensor] = None,
        avg_factor: Optional[int] = None,
        reduction_override: Optional[str] = None
    ) -> torch.Tensor:
        """
        前向传播计算L1损失
        
        Args:
            pred: 预测值
            target: 目标值
            weight: 样本权重
            avg_factor: 平均因子
            reduction_override: 覆盖默认的归约方式
            
        Returns:
            损失值
        """
        assert reduction_override in (None, 'none', 'mean', 'sum')
        reduction = reduction_override if reduction_override else self.reduction
        
        # 计算L1损失
        loss = F.l1_loss(pred, target, reduction='none')
        
        # 应用样本权重
        if weight is not None:
            loss = loss * weight
        
        # 应用归约
        if reduction == 'mean':
            if avg_factor is not None:
                loss = loss.sum() / avg_factor
            else:
                loss = loss.mean()
        elif reduction == 'sum':
            loss = loss.sum()
        
        return self.loss_weight * loss


class CycleCenterNetLoss(nn.Module):
    """
    Cycle-CenterNet组合损失函数

    完整实现论文中的损失函数：
    L_total = L_k + λ_off * L_off + L_p

    其中：
    - L_k: 中心点热图损失（GaussianFocalLoss）
    - L_off: 偏移损失（L1Loss）
    - L_p: 配对损失（Pairing Loss），包含center2vertex和vertex2center损失

    配对损失使用动态加权函数：ω(P_cv) = 1 - exp(-π * D_cv)

    Args:
        heatmap_loss_cfg: 中心点热图损失配置
        offset_loss_cfg: 偏移损失配置
        center2vertex_loss_cfg: center2vertex损失配置
        vertex2center_loss_cfg: vertex2center损失配置
    """

    def __init__(
        self,
        heatmap_loss_cfg: Dict[str, Any] = None,
        offset_loss_cfg: Dict[str, Any] = None,
        center2vertex_loss_cfg: Dict[str, Any] = None,
        vertex2center_loss_cfg: Dict[str, Any] = None
    ):
        super(CycleCenterNetLoss, self).__init__()

        # 中心点热图损失（必需）
        if heatmap_loss_cfg is None:
            heatmap_loss_cfg = dict(alpha=2.0, gamma=4.0, loss_weight=1.0)

        self.heatmap_loss = GaussianFocalLoss(**heatmap_loss_cfg)

        # 偏移损失
        if offset_loss_cfg is None:
            offset_loss_cfg = dict(loss_weight=1.0)
        self.offset_loss = L1Loss(**offset_loss_cfg)

        # center2vertex损失
        if center2vertex_loss_cfg is None:
            center2vertex_loss_cfg = dict(loss_weight=1.0)
        self.center2vertex_loss = L1Loss(**center2vertex_loss_cfg)

        # vertex2center损失
        if vertex2center_loss_cfg is None:
            vertex2center_loss_cfg = dict(loss_weight=0.5)
        self.vertex2center_loss = L1Loss(**vertex2center_loss_cfg)

    def _compute_dynamic_pairing_weight(
        self,
        c2v_pred: torch.Tensor,
        c2v_target: torch.Tensor,
        v2c_pred: Optional[torch.Tensor] = None,
        v2c_target: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """
        动态计算Pairing Loss权重

        根据原始Cycle-CenterNet项目的实现：
        ω(P_cv) = 1 - exp(-π * D_cv)
        其中 D_cv = min(1.0, (|c2v_pred - c2v_target| + |v2c_pred - v2c_target|) / |c2v_target|)

        Args:
            c2v_pred: center2vertex预测值，形状为 (B, 8, H, W)
            c2v_target: center2vertex目标值，形状为 (B, 8, H, W)
            v2c_pred: vertex2center预测值，形状为 (B, 8, H, W)，可选
            v2c_target: vertex2center目标值，形状为 (B, 8, H, W)，可选

        Returns:
            动态权重张量，形状为 (B, 8, H, W)
        """
        # 初始化权重张量
        pairing_weight = torch.zeros_like(c2v_target)

        # 如果没有v2c数据，返回全1权重（简化版本）
        if v2c_pred is None or v2c_target is None:
            # 只在有目标值的位置设置权重为1
            pairing_weight = (c2v_target != 0).float()
            return pairing_weight

        # 计算预测误差
        c2v_error = torch.abs(c2v_pred - c2v_target)
        v2c_error = torch.abs(v2c_pred - v2c_target)

        # 计算归一化误差 D_cv
        # 使用目标偏移的绝对值作为归一化因子，避免除零
        c2v_norm = torch.abs(c2v_target) + 1e-8
        D_cv = torch.clamp((c2v_error + v2c_error) / c2v_norm, max=1.0)

        # 计算动态权重：ω(P_cv) = 1 - exp(-π * D_cv)
        pi = math.pi
        pairing_weight = 1.0 - torch.exp(-pi * D_cv)

        # 只在有目标值的位置应用权重
        valid_mask = (c2v_target != 0) | (v2c_target != 0)
        pairing_weight = pairing_weight * valid_mask.float()

        return pairing_weight
    
    def forward(
        self,
        predictions: Tuple[torch.Tensor, ...],
        targets: Dict[str, torch.Tensor],
        avg_factor: Optional[int] = None
    ) -> Dict[str, torch.Tensor]:
        """
        前向传播计算组合损失
        
        Args:
            predictions: 模型预测结果元组
            targets: 目标字典，包含各种损失的目标值
            avg_factor: 平均因子
            
        Returns:
            损失字典
        """
        losses = {}
        
        # 中心点热图损失
        if len(predictions) >= 1:
            heatmap_pred = predictions[0]  # 第一个分支输出
            heatmap_target = targets['heatmap_target']

            loss_heatmap = self.heatmap_loss(
                pred=heatmap_pred,
                target=heatmap_target,
                avg_factor=avg_factor
            )
            losses['loss_heatmap'] = loss_heatmap
        
        # 2. 偏移损失 (L_off)
        if len(predictions) >= 2 and 'offset_target' in targets:
            offset_pred = predictions[1]  # 第二个分支输出
            offset_target = targets['offset_target']
            offset_target_weight = targets.get('offset_target_weight', None)

            loss_offset = self.offset_loss(
                pred=offset_pred,
                target=offset_target,
                weight=offset_target_weight,
                avg_factor=avg_factor * 2 if avg_factor else None  # 2个通道
            )
            losses['loss_offset'] = loss_offset

        # 3. Pairing Loss (L_p) - center2vertex损失
        if len(predictions) >= 3 and 'center2vertex_target' in targets:
            c2v_pred = predictions[2]  # 第三个分支输出
            c2v_target = targets['center2vertex_target']

            # 动态计算pairing_weight，与原始Cycle-CenterNet项目保持一致
            pairing_weight = self._compute_dynamic_pairing_weight(
                c2v_pred, c2v_target,
                predictions[3] if len(predictions) >= 4 else None,
                targets.get('vertex2center_target', None)
            )

            loss_c2v = self.center2vertex_loss(
                pred=c2v_pred,
                target=c2v_target,
                weight=pairing_weight,
                avg_factor=avg_factor * 8 if avg_factor else None  # 8个通道
            )
            losses['loss_c2v'] = loss_c2v

        # 4. Pairing Loss (L_p) - vertex2center损失
        if len(predictions) >= 4 and 'vertex2center_target' in targets:
            v2c_pred = predictions[3]  # 第四个分支输出
            v2c_target = targets['vertex2center_target']
            c2v_pred = predictions[2]  # center2vertex预测
            c2v_target = targets['center2vertex_target']

            # 动态计算pairing_weight，与原始Cycle-CenterNet项目保持一致
            pairing_weight = self._compute_dynamic_pairing_weight(
                c2v_pred, c2v_target, v2c_pred, v2c_target
            )

            loss_v2c = self.vertex2center_loss(
                pred=v2c_pred,
                target=v2c_target,
                weight=pairing_weight,
                avg_factor=avg_factor * 8 if avg_factor else None  # 8个通道
            )
            losses['loss_v2c'] = loss_v2c
        
        return losses


def create_cycle_centernet_loss(
    version: str = "full",
    heatmap_loss_weight: float = 1.0,
    offset_loss_weight: float = 1.0,
    center2vertex_loss_weight: float = 1.0,
    vertex2center_loss_weight: float = 0.5
) -> CycleCenterNetLoss:
    """
    创建Cycle-CenterNet损失函数
    
    Args:
        version: 版本类型，"simple"为简化版，"full"为完整版
        heatmap_loss_weight: 中心点热图损失权重
        offset_loss_weight: 偏移损失权重
        center2vertex_loss_weight: 中心到顶点损失权重
        vertex2center_loss_weight: 顶点到中心损失权重
        
    Returns:
        损失函数实例
    """
    heatmap_loss_cfg = dict(
        alpha=2.0,
        gamma=4.0,
        loss_weight=heatmap_loss_weight
    )
    
    # 默认创建完整版本，移除简化版本
    offset_loss_cfg = dict(loss_weight=offset_loss_weight)
    center2vertex_loss_cfg = dict(loss_weight=center2vertex_loss_weight)
    vertex2center_loss_cfg = dict(loss_weight=vertex2center_loss_weight)

    return CycleCenterNetLoss(
        heatmap_loss_cfg=heatmap_loss_cfg,
        offset_loss_cfg=offset_loss_cfg,
        center2vertex_loss_cfg=center2vertex_loss_cfg,
        vertex2center_loss_cfg=vertex2center_loss_cfg
    )
