# LORE-TSR 迁移编码计划 - 动态演进版

**架构师:** Cascade
**状态:** 步骤 2 进行中
**日期:** 2025-07-14

---

## 动态迁移蓝图 (The Dynamic Blueprint)

### 1. 文件迁移映射表 (File Migration Map)

| 源文件 (LORE-TSR) | 目标文件 (train-anything) | 迁移策略 (说明) | 状态 |
| :--- | :--- | :--- | :--- |
| `src/lib/opts.py` | `configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` | **转换:** 从 `argparse` 转换为 `OmegaConf` 的 YAML 文件。 | `进行中` |
| `src/lib/opts.py` | `modules/proj_cmd_args/lore_tsr/args.py` | **新建:** 创建新的配置加载和解析模块。 | `进行中` |
| `src/main.py` | `training_loops/table_structure_recognition/train_lore_tsr.py` | **重构:** 废弃原有流程，基于 `Accelerate` 框架重写训练主循环。 | `未开始` |
| `src/lib/datasets/dataset_factory.py` | `my_datasets/table_structure_recognition/lore_tsr_dataset.py` | **适配:** 适配 `train-anything` 的 `TableDataset` 接口。 | `未开始` |
| `src/lib/datasets/sample/ctdet.py` | `my_datasets/table_structure_recognition/lore_tsr_transforms.py` | **重构:** 将数据增强和目标生成逻辑封装为 `TableTransforms`。 | `未开始` |
| `src/lib/models/model.py` | `networks/lore_tsr/model.py` | **封装:** 将模型创建逻辑封装为 `create_lore_tsr_model` 工厂函数。 | `未开始` |
| `src/lib/models/networks/` | `networks/lore_tsr/` | **平移/封装:** 迁移核心网络结构 (DLA, Transformer)。 | `未开始` |
| `src/lib/trains/ctdet.py` (CtdetLoss) | `networks/lore_tsr/loss.py` | **封装:** 将复杂的损失计算逻辑封装为 `LORETSRLoss` 类。 | `未开始` |
| `src/lib/trains/base_trainer.py` | (被废弃) | **废弃:** 训练器逻辑由 `Accelerate` 循环替代。 | `不适用` |
| `src/lib/external/` | `networks/lore_tsr/external/` | **平移:** 迁移需要手动编译的源码 (NMS, DCNv2)。 | `未开始` |
| (N/A) | `training_loops/table_structure_recognition/lore_tsr/README.md` | **新建:** 创建详细的文档。 | `未开始` |


### 2. 目标目录结构树 (Target Directory Tree)

```text
train-anything/
├── configs/
│   └── table_structure_recognition/
│       └── lore_tsr/                                           [✓]
│           └── lore_tsr_config.yaml                          [进行中]
├── modules/
│   └── proj_cmd_args/
│       └── lore_tsr/                                           [✓]
│           └── args.py                                       [进行中]
├── my_datasets/
│   └── table_structure_recognition/
│       └── lore_tsr_dataset.py                               [placeholder]
│       └── lore_tsr_transforms.py                            [placeholder]
├── networks/
│   └── lore_tsr/
│       ├── external/                                           [✓]
│       ├── __init__.py                                       [placeholder]
│       ├── model.py                                          [placeholder]
│       └── loss.py                                           [placeholder]
└── training_loops/
    └── table_structure_recognition/
        ├── lore_tsr/                                           [✓]
        │   └── README.md                                     [placeholder]
        └── train_lore_tsr.py                                   [placeholder]
```

---

## 编码计划：第 2 步

### **步骤 2.1: 迁移核心配置到 YAML 文件**

- **步骤目标:** 将 `LORE-TSR` 项目中 `src/lib/opts.py` 定义的核心命令行参数，迁移到 `train-anything` 框架的 `OmegaConf` 配置文件中。这是实现配置与代码解耦的第一步，也是后续所有模块能够被灵活调用的基础。

- **影响文件:**
  - `train-anything/configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` (新建)

- **具体操作:**
  1.  创建上述 `lore_tsr_config.yaml` 文件。
  2.  分析 `LORE-TSR/src/lib/opts.py` 中的参数，将最核心的参数按照 `train-anything` 的层级结构填入 YAML 文件。本次仅迁移基础、模型、数据、训练和损失相关的核心参数。
  3.  为每个参数添加注释，说明其含义和原始默认值。

- **核心代码内容 (`lore_tsr_config.yaml`):**
  ```yaml
  # @package _global_
  
  # LORE-TSR default configuration
  
  # basic settings
  basic:
    task: ctdet # Task name, fixed to 'ctdet' for cell detection
    exp_id: lore_tsr_dla34 # Experiment ID
    seed: 317 # Random seed
    cudnn_benchmark: false # Whether to use cudnn benchmark
  
  # data settings
  data:
    dataset: table # Dataset type, 'table' for table structure recognition
    data_dir: /path/to/your/dataset # Root directory of the dataset
    num_workers: 4 # Number of data loading workers
  
  # model settings
  model:
    arch: dla_34 # Backbone architecture. e.g., dla_34, resdcn_18, resdcn_101
    head_conv: 256 # Channel number for head convolutions
    down_ratio: 4 # Output stride of the network
    num_classes: 2 # Number of object classes
  
  # training settings
  training:
    lr: 1.25e-4 # Learning rate
    lr_step: [90, 120] # Epochs to decay learning rate
    num_epochs: 140 # Total training epochs
    batch_size: 16 # Training batch size
    master_batch_size: -1 # Batch size on the master GPU, -1 to use batch_size
    gpus: [0] # List of GPUs to use
    val_intervals: 5 # Epochs interval for validation
    save_dir: ../exp/ # Directory to save models and logs
  
  # loss settings
  loss:
    hm_weight: 1 # Weight for heatmap loss
    wh_weight: 0.1 # Weight for size loss
    off_weight: 1 # Weight for offset loss
    s_weight: 0.1 # Weight for structure loss (s_att)
    sc_weight: 1 # Weight for structure loss (sc_att)
  ```

- **如何验证 (Verification):**
  - 检查文件 `train-anything/configs/table_structure_recognition/lore_tsr/lore_tsr_config.yaml` 是否已创建，并且其内容与上述代码一致。

---

### **步骤 2.2: 创建配置解析模块**

- **步骤目标:** 创建一个 Python 模块，使用 `OmegaConf` 库来加载上一步创建的 `lore_tsr_config.yaml` 文件，并支持通过命令行覆盖参数。这是连接配置文件和训练代码的关键桥梁。

- **影响文件:**
  - `train-anything/modules/proj_cmd_args/lore_tsr/args.py` (新建)

- **具体操作:**
  1.  创建 `args.py` 文件。
  2.  编写 `parse_args` 函数，该函数使用 `OmegaConf` 来加载 YAML 文件，并合并来自命令行的任何覆盖选项（例如 `-o model.arch=resdcn_18`）。
  3.  确保该模块没有外部依赖，除了 `OmegaConf` 和 Python 标准库。

- **核心代码内容 (`args.py`):**
  ```python
  import os
  import sys
  from omegaconf import OmegaConf
  
  def parse_args():
      """
      Parses command line arguments and loads the default configuration from a YAML file.
      Allows for overriding configuration values using dot-notation from the command line.
      """
      # Create a base configuration object
      conf = OmegaConf.create()
  
      # Find the default config file relative to this script's location
      # This makes the script runnable from any directory
      current_dir = os.path.dirname(os.path.abspath(__file__))
      default_config_path = os.path.join(
          current_dir, 
          '..', '..', '..', '..', 
          'configs', 'table_structure_recognition', 'lore_tsr', 'lore_tsr_config.yaml'
      )
  
      if not os.path.exists(default_config_path):
          raise FileNotFoundError(f"Default config file not found at: {default_config_path}")
  
      # Load the default config
      default_conf = OmegaConf.load(default_config_path)
      conf.merge_with(default_conf)
  
      # Parse command-line arguments for overrides
      # Example: python train.py -o basic.exp_id=new_experiment model.arch=resdcn_18
      cli_overrides = []
      for i, arg in enumerate(sys.argv):
          if arg == '-o' and i + 1 < len(sys.argv):
              # This is a simple parser; assumes args are like 'key=value'
              # A more robust solution might use argparse for this part
              overrides = sys.argv[i+1:]
              cli_overrides.extend(overrides)
              break # Stop parsing after '-o'
      
      if cli_overrides:
          cli_conf = OmegaConf.from_dotlist(cli_overrides)
          conf.merge_with(cli_conf)
  
      return conf
  
  if __name__ == '__main__':
      # Example of how to use the parser
      config = parse_args()
      print("--- Loaded Configuration ---")
      print(OmegaConf.to_yaml(config))
  
      # Example of accessing a value
      print(f"\nExperiment ID: {config.basic.exp_id}")
      print(f"Model Architecture: {config.model.arch}")
  ```

- **如何验证 (Verification):**
  1.  检查文件 `train-anything/modules/proj_cmd_args/lore_tsr/args.py` 是否已创建且内容正确。
  2.  在 `train-anything` 根目录下运行以下命令，它应该能成功打印出加载的配置信息，证明 YAML 加载和解析逻辑正常工作。

  ```shell
  python ./modules/proj_cmd_args/lore_tsr/args.py
  ```

  3.  运行以下命令测试命令行覆盖功能。输出的 `model.arch` 应该显示为 `resdcn_18`。

  ```shell
  python ./modules/proj_cmd_args/lore_tsr/args.py -o model.arch=resdcn_18
  ```
