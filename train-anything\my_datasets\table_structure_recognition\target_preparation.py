#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025-01-03 17:30
# <AUTHOR> <EMAIL>
# @FileName: target_preparation.py

"""
表格结构识别目标准备模块

负责将标注数据转换为训练目标，包括：
1. 高斯热图生成：将中心点转换为高斯分布热图
2. 偏移目标生成：补偿下采样导致的精度损失
3. 中心到顶点目标：计算从中心点到四个顶点的向量
4. 顶点到中心目标：计算从四个顶点到中心点的向量，用于循环一致性约束

基于原始Cycle-CenterNet项目的实现，支持完整的四分支目标生成。
根据PRD要求，数据预处理相关功能应该在数据集模块中。
"""

import math
from typing import Dict, Any, Tuple, List, Optional

import torch
import numpy as np


def extract_vertices_from_annotation(annotation: Dict[str, Any]) -> np.ndarray:
    """
    从标注数据中提取顶点坐标

    Args:
        annotation: 单个样本的标注数据

    Returns:
        顶点坐标数组，形状为 (N, 4, 2)，顺序为[tl, tr, br, bl]
    """
    vertices_list = []

    if 'cells' in annotation:
        for cell in annotation['cells']:
            if 'bbox' in cell:
                bbox = cell['bbox']
                # 提取四点坐标
                points = []
                for i in range(1, 5):  # p1, p2, p3, p4
                    point_key = f'p{i}'
                    if point_key in bbox:
                        point = bbox[point_key]
                        if isinstance(point, dict) and 'x' in point and 'y' in point:
                            points.append([point['x'], point['y']])
                        elif isinstance(point, (list, tuple)) and len(point) == 2:
                            points.append([point[0], point[1]])

                if len(points) == 4:
                    # 假设p1=tl, p2=tr, p3=br, p4=bl
                    vertices = np.array(points, dtype=np.float32)
                    vertices_list.append(vertices)

    if vertices_list:
        return np.array(vertices_list, dtype=np.float32)
    else:
        return np.zeros((0, 4, 2), dtype=np.float32)


def create_gaussian_heatmap_target(
    center_points: np.ndarray,
    vertex_points: Optional[np.ndarray] = None,
    image_shape: Optional[Tuple[int, int]] = None,
    radius: int = 3
) -> torch.Tensor:
    """
    创建高斯热图目标，支持单通道和双通道模式

    根据 vertex_points 参数自动判断：
    - vertex_points 为 None 或空：单通道模式，仅生成中心点热图 (1, H, W)
    - vertex_points 有效：双通道模式，生成中心点+顶点热图 (2, H, W)

    Args:
        center_points: 中心点坐标，形状为 (N, 2)
        vertex_points: 顶点坐标，形状为 (N, 4, 2)，顺序为[tl, tr, br, bl]。None表示单通道模式
        image_shape: 图像尺寸 (height, width)
        radius: 高斯半径

    Returns:
        热图目标，形状为 (1, H, W) 或 (2, H, W)
    """
    if image_shape is None:
        raise ValueError("image_shape cannot be None")

    height, width = image_shape
    sigma = radius / 3.0

    # 判断是否为双通道模式
    is_dual_channel = vertex_points is not None and len(vertex_points) > 0
    num_channels = 2 if is_dual_channel else 1

    heatmap = np.zeros((num_channels, height, width), dtype=np.float32)

    # 通道0：单元格中心点热图
    for center_x, center_y in center_points:
        # 确保中心点在图像范围内
        center_x = int(np.clip(center_x, 0, width - 1))
        center_y = int(np.clip(center_y, 0, height - 1))

        # 创建高斯分布
        y, x = np.ogrid[:height, :width]
        dist_sq = (x - center_x) ** 2 + (y - center_y) ** 2
        gaussian = np.exp(-dist_sq / (2 * sigma ** 2))

        # 取最大值（处理重叠情况）
        heatmap[0] = np.maximum(heatmap[0], gaussian)

    # 通道1：顶点热图（仅在双通道模式下）
    if is_dual_channel and vertex_points is not None:
        for vertices in vertex_points:  # vertices 形状为 (4, 2)
            for vx, vy in vertices:
                # 确保顶点在图像范围内
                vx = int(np.clip(vx, 0, width - 1))
                vy = int(np.clip(vy, 0, height - 1))

                # 避免重复生成相同位置的高斯分布
                if heatmap[1, vy, vx] != 1.0:
                    # 创建高斯分布
                    y, x = np.ogrid[:height, :width]
                    dist_sq = (x - vx) ** 2 + (y - vy) ** 2
                    gaussian = np.exp(-dist_sq / (2 * sigma ** 2))

                    # 取最大值（处理重叠情况）
                    heatmap[1] = np.maximum(heatmap[1], gaussian)

    return torch.from_numpy(heatmap)  # 形状为 (1, H, W) 或 (2, H, W)


def create_offset_target(
    center_points: np.ndarray,
    image_shape: Tuple[int, int]
) -> torch.Tensor:
    """
    创建偏移目标

    偏移目标用于补偿由于下采样导致的精度损失。
    对于每个中心点，计算其在特征图上的偏移量。

    Args:
        center_points: 中心点坐标，形状为 (N, 2)
        image_shape: 图像尺寸 (height, width)

    Returns:
        偏移目标，形状为 (2, H, W)
    """
    height, width = image_shape
    offset_target = torch.zeros(2, height, width, dtype=torch.float32)

    for center_x, center_y in center_points:
        # 计算在特征图上的整数坐标
        center_x_int = int(center_x)
        center_y_int = int(center_y)

        # 确保坐标在有效范围内
        if 0 <= center_x_int < width and 0 <= center_y_int < height:
            # 计算偏移量（浮点坐标 - 整数坐标，确保类型兼容）
            offset_x = float(center_x - center_x_int)
            offset_y = float(center_y - center_y_int)

            # 设置偏移目标
            offset_target[0, center_y_int, center_x_int] = offset_x
            offset_target[1, center_y_int, center_x_int] = offset_y

    return offset_target


def create_center2vertex_target(
    center_points: np.ndarray,
    vertex_points: np.ndarray,
    image_shape: Tuple[int, int]
) -> torch.Tensor:
    """
    创建中心到顶点目标

    计算从中心点到四个顶点的向量，用于表格单元格的精确定位。
    输出8个通道：[tl_x, tl_y, tr_x, tr_y, br_x, br_y, bl_x, bl_y]

    Args:
        center_points: 中心点坐标，形状为 (N, 2)
        vertex_points: 顶点坐标，形状为 (N, 4, 2)，顺序为[tl, tr, br, bl]
        image_shape: 图像尺寸 (height, width)

    Returns:
        中心到顶点目标，形状为 (8, H, W)
    """
    height, width = image_shape
    center2vertex_target = torch.zeros(8, height, width, dtype=torch.float32)

    for i, (center_x, center_y) in enumerate(center_points):
        # 计算在特征图上的整数坐标
        center_x_int = int(center_x)
        center_y_int = int(center_y)

        # 确保坐标在有效范围内
        if 0 <= center_x_int < width and 0 <= center_y_int < height and i < len(vertex_points):
            vertices = vertex_points[i]  # 形状为 (4, 2)

            # 计算从中心点到四个顶点的向量
            for j, (vx, vy) in enumerate(vertices):
                # 计算向量（确保类型兼容）
                vec_x = float(vx - center_x)
                vec_y = float(vy - center_y)

                # 存储到对应通道
                center2vertex_target[j * 2, center_y_int, center_x_int] = vec_x
                center2vertex_target[j * 2 + 1, center_y_int, center_x_int] = vec_y

    return center2vertex_target


def create_vertex2center_target(
    center_points: np.ndarray,
    vertex_points: np.ndarray,
    image_shape: Tuple[int, int]
) -> torch.Tensor:
    """
    创建顶点到中心目标

    计算从四个顶点到中心点的向量，用于循环一致性约束。
    输出8个通道：[tl_x, tl_y, tr_x, tr_y, br_x, br_y, bl_x, bl_y]

    Args:
        center_points: 中心点坐标，形状为 (N, 2)
        vertex_points: 顶点坐标，形状为 (N, 4, 2)，顺序为[tl, tr, br, bl]
        image_shape: 图像尺寸 (height, width)

    Returns:
        顶点到中心目标，形状为 (8, H, W)
    """
    height, width = image_shape
    vertex2center_target = torch.zeros(8, height, width, dtype=torch.float32)

    for i, center_point in enumerate(center_points):
        if i < len(vertex_points):
            center_x, center_y = center_point
            vertices = vertex_points[i]  # 形状为 (4, 2)

            # 对每个顶点计算到中心点的向量
            for j, (vx, vy) in enumerate(vertices):
                # 计算顶点在特征图上的整数坐标
                vx_int = int(vx)
                vy_int = int(vy)

                # 确保坐标在有效范围内
                if 0 <= vx_int < width and 0 <= vy_int < height:
                    # 计算从顶点到中心点的向量（确保类型兼容）
                    vec_x = float(center_x - vx)
                    vec_y = float(center_y - vy)

                    # 存储到对应通道
                    vertex2center_target[j * 2, vy_int, vx_int] = vec_x
                    vertex2center_target[j * 2 + 1, vy_int, vx_int] = vec_y

    return vertex2center_target


def create_offset_target_weight(
    center_points: np.ndarray,
    image_shape: Tuple[int, int]
) -> torch.Tensor:
    """
    创建偏移目标权重

    为有中心点的位置设置权重为1，其他位置为0

    Args:
        center_points: 中心点坐标，形状为 (N, 2)
        image_shape: 图像尺寸 (height, width)

    Returns:
        偏移目标权重，形状为 (2, H, W)
    """
    height, width = image_shape
    offset_weight = torch.zeros(2, height, width, dtype=torch.float32)

    for center_x, center_y in center_points:
        # 计算中心点在特征图上的整数坐标
        center_x_int = int(center_x)
        center_y_int = int(center_y)

        # 确保坐标在有效范围内
        if 0 <= center_x_int < width and 0 <= center_y_int < height:
            # 为有中心点的位置设置权重
            offset_weight[:, center_y_int, center_x_int] = 1.0

    return offset_weight


def create_pairing_weight(
    center_points: np.ndarray,
    vertex_points: np.ndarray,
    center2vertex_pred: torch.Tensor,
    vertex2center_pred: torch.Tensor,
    center2vertex_target: torch.Tensor,
    vertex2center_target: torch.Tensor,
    image_shape: Tuple[int, int]
) -> torch.Tensor:
    """
    创建配对损失权重

    根据论文公式：ω(P_cv) = 1 - exp(-π * D_cv)
    其中 D_cv 是归一化后的中心-顶点对的偏移误差

    Args:
        center_points: 中心点坐标，形状为 (N, 2)
        vertex_points: 顶点坐标，形状为 (N, 4, 2)
        center2vertex_pred: center2vertex预测值
        vertex2center_pred: vertex2center预测值
        center2vertex_target: center2vertex目标值
        vertex2center_target: vertex2center目标值
        image_shape: 图像尺寸 (height, width)

    Returns:
        配对权重，形状为 (8, H, W)
    """
    height, width = image_shape
    pairing_weight = torch.zeros(8, height, width, dtype=torch.float32)

    pi = math.pi

    for i, center_point in enumerate(center_points):
        if i < len(vertex_points):
            center_x, center_y = center_point
            vertices = vertex_points[i]  # 形状为 (4, 2)

            # 计算中心点在特征图上的整数坐标
            center_x_int = int(center_x)
            center_y_int = int(center_y)

            # 确保中心点坐标在有效范围内
            if 0 <= center_x_int < width and 0 <= center_y_int < height:
                # 对每个顶点计算动态权重
                for j, (vx, vy) in enumerate(vertices):
                    vx_int = int(vx)
                    vy_int = int(vy)

                    # 确保顶点坐标在有效范围内
                    if 0 <= vx_int < width and 0 <= vy_int < height:
                        # 计算预测误差
                        for k in range(2):  # x, y 两个通道
                            channel_idx = j * 2 + k

                            # 获取预测值和目标值
                            c2v_pred_val = center2vertex_pred[channel_idx, center_y_int, center_x_int]
                            c2v_target_val = center2vertex_target[channel_idx, center_y_int, center_x_int]
                            v2c_pred_val = vertex2center_pred[channel_idx, vy_int, vx_int]
                            v2c_target_val = vertex2center_target[channel_idx, vy_int, vx_int]

                            # 计算归一化误差 D_cv
                            c2v_error = abs(c2v_pred_val - c2v_target_val)
                            v2c_error = abs(v2c_pred_val - v2c_target_val)
                            c2v_norm = abs(c2v_target_val) + 1e-8  # 避免除零

                            D_cv = min(1.0, (c2v_error + v2c_error) / c2v_norm)

                            # 计算动态权重：ω(P_cv) = 1 - exp(-π * D_cv)
                            weight = 1.0 - math.exp(-pi * D_cv)

                            # 设置权重
                            pairing_weight[channel_idx, center_y_int, center_x_int] += weight
                            pairing_weight[channel_idx, vy_int, vx_int] += weight

    return pairing_weight


def prepare_targets(
    batch_data: Dict[str, Any],
    output_size: Tuple[int, int] = (128, 128),
    head_version: str = "full",
    heatmap_channels: int = 1
) -> Dict[str, torch.Tensor]:
    """
    准备训练目标

    Args:
        batch_data: 批次数据
        output_size: 输出特征图尺寸，默认为(128, 128)，对应4倍下采样
        head_version: 检测头版本，只支持'full'
        heatmap_channels: 热图通道数，1=单通道(仅中心点)，2=双通道(中心点+顶点)

    Returns:
        目标字典
    """
    batch_size = len(batch_data['cell_centers'])
    input_shape = batch_data['images'].shape[2:]  # (H, W)
    
    # 计算下采样比例
    scale_x = output_size[1] / input_shape[1]  # width scale
    scale_y = output_size[0] / input_shape[0]  # height scale
    
    # 准备热图目标
    heatmap_targets = []
    
    for i in range(batch_size):
        cell_centers = batch_data['cell_centers'][i]

        if len(cell_centers) > 0:
            # 确保cell_centers是numpy数组
            if isinstance(cell_centers, torch.Tensor):
                cell_centers_np = cell_centers.numpy()
            else:
                cell_centers_np = cell_centers.copy()

            # 将中心点坐标缩放到输出特征图尺寸
            cell_centers_scaled = cell_centers_np.astype(np.float32)
            cell_centers_scaled[:, 0] *= scale_x  # x坐标
            cell_centers_scaled[:, 1] *= scale_y  # y坐标

            # 根据heatmap_channels决定是否提取顶点信息
            vertex_points = None
            if heatmap_channels == 2:
                # 双通道热图：需要提取顶点信息
                if 'annotation' in batch_data and len(batch_data['annotation']) > i:
                    annotation = batch_data['annotation'][i]
                    vertex_points = extract_vertices_from_annotation(annotation)
                    if len(vertex_points) > 0:
                        # 缩放顶点坐标到输出特征图尺寸
                        vertex_points = vertex_points.copy()
                        vertex_points[:, :, 0] *= scale_x  # x坐标
                        vertex_points[:, :, 1] *= scale_y  # y坐标

                # 如果没有从标注中提取到顶点，尝试从边界框生成
                if vertex_points is None or len(vertex_points) == 0:
                    if 'bboxes' in batch_data and len(batch_data['bboxes'][i]) > 0:
                        bboxes = batch_data['bboxes'][i]
                        # 将边界框转换为顶点坐标 [tl, tr, br, bl]
                        vertex_points_list = []
                        for bbox in bboxes:
                            x_min, y_min, x_max, y_max = bbox
                            # 缩放到输出特征图尺寸
                            x_min_scaled = x_min * scale_x
                            y_min_scaled = y_min * scale_y
                            x_max_scaled = x_max * scale_x
                            y_max_scaled = y_max * scale_y

                            vertices = np.array([
                                [x_min_scaled, y_min_scaled],  # top-left
                                [x_max_scaled, y_min_scaled],  # top-right
                                [x_max_scaled, y_max_scaled],  # bottom-right
                                [x_min_scaled, y_max_scaled]   # bottom-left
                            ])
                            vertex_points_list.append(vertices)

                        vertex_points = np.array(vertex_points_list, dtype=np.float32)

            # 统一使用create_gaussian_heatmap_target，根据vertex_points自动判断单/双通道
            heatmap_target = create_gaussian_heatmap_target(
                cell_centers_scaled,
                vertex_points,  # None表示单通道，有值表示双通道
                output_size
            )
        else:
            # 没有目标时创建全零热图
            heatmap_target = torch.zeros(heatmap_channels, *output_size)

        heatmap_targets.append(heatmap_target)
    
    # 堆叠成批次
    heatmap_targets = torch.stack(heatmap_targets, dim=0)
    
    targets = {'heatmap_target': heatmap_targets}
    
    # 如果是完整版，添加其他目标
    if head_version == "full":
        offset_targets = []
        offset_target_weights = []
        center2vertex_targets = []
        vertex2center_targets = []
        # pairing_weights现在在损失函数中动态计算，不再在这里生成

        for i in range(batch_size):
            cell_centers = batch_data['cell_centers'][i]

            if len(cell_centers) > 0:
                # 确保cell_centers是numpy数组
                if isinstance(cell_centers, torch.Tensor):
                    cell_centers_np = cell_centers.numpy()
                else:
                    cell_centers_np = cell_centers.copy()

                # 将中心点坐标缩放到输出特征图尺寸
                cell_centers_scaled = cell_centers_np.astype(np.float32)
                cell_centers_scaled[:, 0] *= scale_x  # x坐标
                cell_centers_scaled[:, 1] *= scale_y  # y坐标

                # 创建偏移目标和权重
                offset_target = create_offset_target(cell_centers_scaled, output_size)
                offset_target_weight = create_offset_target_weight(cell_centers_scaled, output_size)

                # 尝试从原始标注数据中提取顶点信息
                vertex_points = None
                if 'annotation' in batch_data and len(batch_data['annotation']) > i:
                    annotation = batch_data['annotation'][i]
                    vertex_points = extract_vertices_from_annotation(annotation)
                    if len(vertex_points) > 0:
                        # 缩放顶点坐标到输出特征图尺寸
                        vertex_points = vertex_points.copy()
                        vertex_points[:, :, 0] *= scale_x  # x坐标
                        vertex_points[:, :, 1] *= scale_y  # y坐标

                # 如果没有从标注中提取到顶点，尝试从边界框生成
                if vertex_points is None or len(vertex_points) == 0:
                    if 'bboxes' in batch_data and len(batch_data['bboxes'][i]) > 0:
                        bboxes = batch_data['bboxes'][i]
                        # 将边界框转换为顶点坐标 [tl, tr, br, bl]
                        vertex_points_list = []
                        for bbox in bboxes:
                            x_min, y_min, x_max, y_max = bbox
                            # 缩放到输出特征图尺寸
                            x_min_scaled = x_min * scale_x
                            y_min_scaled = y_min * scale_y
                            x_max_scaled = x_max * scale_x
                            y_max_scaled = y_max * scale_y

                            vertices = np.array([
                                [x_min_scaled, y_min_scaled],  # top-left
                                [x_max_scaled, y_min_scaled],  # top-right
                                [x_max_scaled, y_max_scaled],  # bottom-right
                                [x_min_scaled, y_max_scaled]   # bottom-left
                            ])
                            vertex_points_list.append(vertices)

                        vertex_points = np.array(vertex_points_list, dtype=np.float32)

                # 创建顶点相关目标
                if vertex_points is not None and len(vertex_points) > 0:
                    center2vertex_target = create_center2vertex_target(
                        cell_centers_scaled, vertex_points, output_size
                    )
                    vertex2center_target = create_vertex2center_target(
                        cell_centers_scaled, vertex_points, output_size
                    )

                    # 配对权重现在在损失函数中动态计算，这里不再需要
                    pairing_weight = None
                else:
                    # 没有顶点信息时创建全零目标
                    center2vertex_target = torch.zeros(8, *output_size)
                    vertex2center_target = torch.zeros(8, *output_size)
                    pairing_weight = None
            else:
                # 没有目标时创建全零目标
                offset_target = torch.zeros(2, *output_size)
                offset_target_weight = torch.zeros(2, *output_size)
                center2vertex_target = torch.zeros(8, *output_size)
                vertex2center_target = torch.zeros(8, *output_size)
                pairing_weight = None

            offset_targets.append(offset_target)
            offset_target_weights.append(offset_target_weight)
            center2vertex_targets.append(center2vertex_target)
            vertex2center_targets.append(vertex2center_target)

        # 堆叠成批次
        offset_targets = torch.stack(offset_targets, dim=0)
        offset_target_weights = torch.stack(offset_target_weights, dim=0)
        center2vertex_targets = torch.stack(center2vertex_targets, dim=0)
        vertex2center_targets = torch.stack(vertex2center_targets, dim=0)

        targets.update({
            'offset_target': offset_targets,
            'offset_target_weight': offset_target_weights,
            'center2vertex_target': center2vertex_targets,
            'vertex2center_target': vertex2center_targets,
            # pairing_weight现在在损失函数中动态计算
        })
    
    return targets


class TargetGenerator:
    """
    目标生成器类
    
    封装目标生成逻辑，便于配置和扩展
    """
    
    def __init__(
        self,
        head_version: str = "full",
        gaussian_radius: int = 3,
        output_stride: int = 4,
        heatmap_channels: int = 1
    ):
        """
        初始化目标生成器

        Args:
            head_version: 检测头版本
            gaussian_radius: 高斯半径
            output_stride: 输出步长（下采样倍数）
            heatmap_channels: 热图通道数，1=单通道(仅中心点)，2=双通道(中心点+顶点)
        """
        self.head_version = head_version
        self.gaussian_radius = gaussian_radius
        self.output_stride = output_stride
        self.heatmap_channels = heatmap_channels
    
    def __call__(
        self,
        batch_data: Dict[str, Any],
        input_size: Tuple[int, int]
    ) -> Dict[str, torch.Tensor]:
        """
        生成训练目标
        
        Args:
            batch_data: 批次数据
            input_size: 输入图像尺寸
            
        Returns:
            目标字典
        """
        output_size = (
            input_size[0] // self.output_stride,
            input_size[1] // self.output_stride
        )
        
        return prepare_targets(
            batch_data,
            output_size,
            self.head_version,
            self.heatmap_channels
        )


def create_full_target_generator(**kwargs) -> TargetGenerator:
    """创建完整版目标生成器"""
    return TargetGenerator(head_version="full", **kwargs)
