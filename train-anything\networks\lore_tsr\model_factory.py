# Time: 2025-07-16
# Author: AI Assistant
# FileName: model_factory.py

"""
LORE-TSR 模型工厂

这个模块包含了从 LORE-TSR 项目迁移的模型创建逻辑，负责根据配置参数
创建相应的骨干网络模型。

主要功能:
- 支持多种骨干网络架构 (DLA, ResNet-FPN 系列)
- 提供统一的模型创建接口
- 处理模型参数和头部配置
"""

from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import torchvision.models as models
import torch
import torch.nn as nn
import os

# 导入所有支持的骨干网络
from .fpn_resnet import get_pose_net_fpn
from .fpn_resnet_half import get_pose_net_fpn_half
from .fpn_mask_resnet import get_pose_net_fpn_mask
from .fpn_mask_resnet_half import get_pose_net_fpn_mask_half
from .pose_dla_dcn import get_pose_net as get_dla_dcn


# 模型工厂字典 - 将架构名称映射到对应的构造函数
_model_factory = {
    'dla': get_dla_dcn, 
    'resfpn': get_pose_net_fpn,
    'resfpnhalf': get_pose_net_fpn_half,
    'resfpnmask': get_pose_net_fpn_mask,
    'resfpnmaskhalf': get_pose_net_fpn_mask_half
}


def create_model(arch, heads, head_conv):
    """
    创建 LORE-TSR 模型
    
    Args:
        arch (str): 架构名称，格式为 'backbone' 或 'backbone_layers'
                   支持的backbone: dla, resfpn, resfpnhalf, resfpnmask, resfpnmaskhalf
        heads (dict): 输出头部配置，包含各个任务的输出通道数
        head_conv (int): 头部卷积层的通道数
    
    Returns:
        torch.nn.Module: 创建的模型实例
    
    Examples:
        >>> heads = {'hm': 1, 'wh': 2, 'reg': 2}
        >>> model = create_model('dla_34', heads, 256)
        >>> model = create_model('resfpn_18', heads, 64)
    """
    # 解析架构名称，提取层数信息
    num_layers = int(arch[arch.find('_') + 1:]) if '_' in arch else 0
    arch = arch[:arch.find('_')] if '_' in arch else arch
    
    # 从工厂字典中获取对应的模型构造函数
    get_model = _model_factory[arch]
    
    # 创建模型实例
    model = get_model(num_layers=num_layers, heads=heads, head_conv=head_conv)
    
    return model


def get_supported_architectures():
    """
    获取所有支持的架构名称
    
    Returns:
        list: 支持的架构名称列表
    """
    return list(_model_factory.keys())


def is_architecture_supported(arch):
    """
    检查指定的架构是否被支持
    
    Args:
        arch (str): 架构名称
    
    Returns:
        bool: 如果架构被支持返回True，否则返回False
    """
    # 提取基础架构名称（去除层数后缀）
    base_arch = arch[:arch.find('_')] if '_' in arch else arch
    return base_arch in _model_factory
